/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
/* tslint:disable */
// Copyright 2024 Google LLC

// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at

//     https://www.apache.org/licenses/LICENSE-2.0

// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import {useAtom, useAtomValue} from 'jotai';
import type {SetStateAction} from 'jotai';
import getStroke from 'perfect-freehand';
import {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {ResizePayload, useResizeDetector} from 'react-resize-detector';
import {
  ActiveColorAtom,
  BoundingBoxes2DAtom,
  BoundingBoxMasksAtom,
  DetectTypeAtom,
  DrawModeAtom,
  HoveredBoxAtom,
  ImageSentAtom,
  ImageSrcAtom,
  LabelColorMapAtom,
  LinesAtom,
  MediaDimensionsAtom,
  PointsAtom,
  RevealOnHoverModeAtom,
  ShareStream,
  VideoRefAtom,
  ActiveResultIndexAtom,
  DraggingPointIndexAtom,
  ChatHistoryAtom,
  Dragging2DBoxAtom,
  RefineBoxRequestAtom,
} from './atoms';
import {lineOptions, segmentationColorsRgb} from './consts';
import {getSvgPathFromStroke} from './utils';
import {ChatMessage, MediaDimensions, PointType, BoundingBox2DType} from './Types';

export function Content() {
  const [imageSrc] = useAtom(ImageSrcAtom);
  const [boundingBoxes2D, setBoundingBoxes2D] = useAtom(BoundingBoxes2DAtom) as [
    (BoundingBox2DType | null)[],
    (u: SetStateAction<(BoundingBox2DType | null)[]>) => void,
  ];
  const [boundingBoxMasks] = useAtom(BoundingBoxMasksAtom);
  const [stream] = useAtom(ShareStream);
  const [detectType] = useAtom(DetectTypeAtom);
  const [videoRef] = useAtom(VideoRefAtom);
  const [, setImageSent] = useAtom(ImageSentAtom) as [
    any,
    (u: SetStateAction<boolean>) => void,
  ];
  const [points, setPoints] = useAtom(PointsAtom) as [
    (PointType | null)[],
    (u: SetStateAction<(PointType | null)[]>) => void,
  ];
  const labelColorMap = useAtomValue(LabelColorMapAtom);
  const [revealOnHover] = useAtom(RevealOnHoverModeAtom);
  const [hoverEntered, setHoverEntered] = useState(false);
  const [hoveredBox, setHoveredBox] = useAtom(HoveredBoxAtom) as [
    number | null,
    (u: SetStateAction<number | null>) => void,
  ];
  const [activeResultIndex, setActiveResultIndex] = useAtom(
    ActiveResultIndexAtom,
  ) as [number | null, (u: SetStateAction<number | null>) => void];
  const [draggingPointIndex, setDraggingPointIndex] = useAtom(
    DraggingPointIndexAtom,
  ) as [number | null, (u: SetStateAction<number | null>) => void];
  const [dragging2DBox, setDragging2DBox] = useAtom(Dragging2DBoxAtom) as [
    {index: number; handle: 'body' | 'tl' | 'tr' | 'bl' | 'br'} | null,
    (
      u: SetStateAction<{
        index: number;
        handle: 'body' | 'tl' | 'tr' | 'bl' | 'br';
      } | null>,
    ) => void,
  ];
  const dragStartRef = useRef<{x: number; y: number; box: BoundingBox2DType} | null>(null);

  const [drawMode, setDrawMode] = useAtom(DrawModeAtom) as [
    boolean,
    (u: SetStateAction<boolean>) => void,
  ];
  const [lines, setLines] = useAtom(LinesAtom) as [
    [[number, number][], string][],
    (u: SetStateAction<[[number, number][], string][]>) => void,
  ];
  const [activeColor] = useAtom(ActiveColorAtom);
  const [, setChatHistory] = useAtom(ChatHistoryAtom) as [
    any,
    (u: SetStateAction<ChatMessage[]>) => void,
  ];
  const [, setRefineBoxRequest] = useAtom(RefineBoxRequestAtom) as [
    any,
    (u: SetStateAction<{index: number; box: BoundingBox2DType} | null>) => void,
  ];

  // Handling resize and aspect ratios
  const boundingBoxContainerRef = useRef<HTMLDivElement | null>(null);
  const [containerDims, setContainerDims] = useState({
    width: 0,
    height: 0,
  });
  const [activeMediaDimensions, setActiveMediaDimensions] =
    useAtom(MediaDimensionsAtom);

  const onResize = useCallback((el: ResizePayload) => {
    if (el.width && el.height) {
      setContainerDims({
        width: el.width,
        height: el.height,
      });
    }
  }, []);

  const {ref: containerRef} = useResizeDetector({onResize});

  const boundingBoxContainer = useMemo(() => {
    const {width, height} = activeMediaDimensions;
    if (width === 0 || height === 0) {
      return {width: 0, height: 0};
    }
    const aspectRatio = width / height;
    const containerAspectRatio = containerDims.width / containerDims.height;
    if (aspectRatio < containerAspectRatio) {
      return {
        height: containerDims.height,
        width: containerDims.height * aspectRatio,
      };
    } else {
      return {
        width: containerDims.width,
        height: containerDims.width / aspectRatio,
      };
    }
  }, [containerDims, activeMediaDimensions]);

  function updateHoveredBoxOnPointerEvent(e: React.PointerEvent) {
    const boxes = document.querySelectorAll('.bbox');
    const dimensionsAndIndex = Array.from(boxes)
      .map((box) => {
        const {top, left, width, height} = box.getBoundingClientRect();
        const index = parseInt(box.getAttribute('data-index') || '-1', 10);
        return {
          top,
          left,
          width,
          height,
          index,
        };
      })
      .filter((box) => box.index !== -1);
    // Sort smallest to largest
    const sorted = dimensionsAndIndex.sort(
      (a, b) => a.width * a.height - b.width * b.height,
    );
    // Find the smallest box that contains the mouse
    const {clientX, clientY} = e;
    const found = sorted.find(({top, left, width, height}) => {
      return (
        clientX > left &&
        clientX < left + width &&
        clientY > top &&
        clientY < top + height
      );
    });
    if (found) {
      setHoveredBox(found.index);
    } else {
      setHoveredBox(null);
    }
  }

  const downRef = useRef<Boolean>(false);

  return (
    <div ref={containerRef} className="w-full h-full relative">
      {stream ? (
        <video
          className="absolute top-0 left-0 w-full h-full object-contain"
          autoPlay
          onLoadedMetadata={(e) => {
            setActiveMediaDimensions({
              width: e.currentTarget.videoWidth,
              height: e.currentTarget.videoHeight,
            });
          }}
          ref={(video) => {
            videoRef.current = video;
            if (video && !video.srcObject) {
              video.srcObject = stream;
            }
          }}
        />
      ) : imageSrc ? (
        <img
          src={imageSrc}
          className="absolute top-0 left-0 w-full h-full object-contain"
          alt="Uploaded image"
          onLoad={(e) => {
            setActiveMediaDimensions({
              width: e.currentTarget.naturalWidth,
              height: e.currentTarget.naturalHeight,
            });
          }}
        />
      ) : null}
      <div
        className={`absolute w-full h-full left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 ${hoverEntered ? 'hide-box' : ''} ${draggingPointIndex !== null || dragging2DBox ? 'grabbing' : drawMode ? 'cursor-crosshair' : ''}`}
        ref={boundingBoxContainerRef}
        onPointerEnter={(e) => {
          if (revealOnHover && !drawMode) {
            setHoverEntered(true);
            updateHoveredBoxOnPointerEvent(e);
          }
        }}
        onPointerMove={(e) => {
          if (revealOnHover && !drawMode && draggingPointIndex === null && !dragging2DBox) {
            setHoverEntered(true);
            updateHoveredBoxOnPointerEvent(e);
          }
          if (downRef.current) {
            const parentBounds =
              boundingBoxContainerRef.current!.getBoundingClientRect();
            setLines((prev) => [
              ...prev.slice(0, prev.length - 1),
              [
                [
                  ...prev[prev.length - 1][0],
                  [
                    (e.clientX - parentBounds.left) /
                      boundingBoxContainer!.width,
                    (e.clientY - parentBounds.top) /
                      boundingBoxContainer!.height,
                  ],
                ],
                prev[prev.length - 1][1],
              ],
            ]);
          }
          if (draggingPointIndex !== null) {
            const parentBounds =
              boundingBoxContainerRef.current!.getBoundingClientRect();
            let newX =
              (e.clientX - parentBounds.left) / boundingBoxContainer!.width;
            let newY =
              (e.clientY - parentBounds.top) / boundingBoxContainer!.height;
            // Clamp values
            newX = Math.max(0, Math.min(1, newX));
            newY = Math.max(0, Math.min(1, newY));

            setPoints((prev) =>
              prev.map((p, index) => {
                if (index === draggingPointIndex && p) {
                  return {
                    ...p,
                    point: {x: newX, y: newY},
                  };
                }
                return p;
              }),
            );
          }
           if (dragging2DBox) {
            const parentBounds = boundingBoxContainerRef.current!.getBoundingClientRect();
            const currentX = (e.clientX - parentBounds.left) / boundingBoxContainer.width;
            const currentY = (e.clientY - parentBounds.top) / boundingBoxContainer.height;
            
            setBoundingBoxes2D(prev => prev.map((box, index) => {
              if (index !== dragging2DBox.index || !box) return box;

              const dx = currentX - dragStartRef.current!.x;
              const dy = currentY - dragStartRef.current!.y;
              const startBox = dragStartRef.current!.box;
              let newBox = {...box};

              switch (dragging2DBox.handle) {
                case 'body':
                  newBox.x = startBox.x + dx;
                  newBox.y = startBox.y + dy;
                  break;
                case 'tl':
                  newBox.x = startBox.x + dx;
                  newBox.y = startBox.y + dy;
                  newBox.width = startBox.width - dx;
                  newBox.height = startBox.height - dy;
                  break;
                case 'tr':
                  newBox.y = startBox.y + dy;
                  newBox.width = startBox.width + dx;
                  newBox.height = startBox.height - dy;
                  break;
                case 'bl':
                   newBox.x = startBox.x + dx;
                   newBox.width = startBox.width - dx;
                   newBox.height = startBox.height + dy;
                  break;
                case 'br':
                   newBox.width = startBox.width + dx;
                   newBox.height = startBox.height + dy;
                  break;
              }

              // Ensure dimensions are positive
              if (newBox.width < 0) {
                  newBox.x += newBox.width;
                  newBox.width = -newBox.width;
              }
              if (newBox.height < 0) {
                  newBox.y += newBox.height;
                  newBox.height = -newBox.height;
              }
              
              // Clamp values to be within 0-1 range
              newBox.x = Math.max(0, newBox.x);
              newBox.y = Math.max(0, newBox.y);
              newBox.width = Math.min(1 - newBox.x, newBox.width);
              newBox.height = Math.min(1 - newBox.y, newBox.height);

              return newBox;
            }));
          }
        }}
        onPointerLeave={(e) => {
          if (revealOnHover && !drawMode) {
            setHoverEntered(false);
            setHoveredBox(null);
          }
        }}
        onPointerDown={(e) => {
          if (drawMode) {
            setImageSent(false);
            (e.target as HTMLElement).setPointerCapture(e.pointerId);
            downRef.current = true;
            const parentBounds =
              boundingBoxContainerRef.current!.getBoundingClientRect();
            setLines((prev) => [
              ...prev,
              [
                [
                  [
                    (e.clientX - parentBounds.left) /
                      boundingBoxContainer!.width,
                    (e.clientY - parentBounds.top) /
                      boundingBoxContainer!.height,
                  ],
                ],
                activeColor,
              ],
            ]);
          }
        }}
        onPointerUp={(e) => {
          if (drawMode) {
            (e.target as HTMLElement).releasePointerCapture(e.pointerId);
            downRef.current = false;
          }
          if (draggingPointIndex !== null) {
            (e.target as HTMLElement).releasePointerCapture(e.pointerId);

            const parentBounds =
              boundingBoxContainerRef.current!.getBoundingClientRect();
            let finalX =
              (e.clientX - parentBounds.left) / boundingBoxContainer!.width;
            let finalY =
              (e.clientY - parentBounds.top) / boundingBoxContainer!.height;
            finalX = Math.max(0, Math.min(1, finalX));
            finalY = Math.max(0, Math.min(1, finalY));
            const finalPointCoords = {x: finalX, y: finalY};

            setPoints((prev) =>
              prev.map((p, index) => {
                if (index === draggingPointIndex && p) {
                  return {...p, point: finalPointCoords};
                }
                return p;
              }),
            );

            setChatHistory((prevHistory) => {
              const newHistory = [...prevHistory];
              const lastModelMessageIndex = newHistory
                .map((msg) => msg.role === 'model' && !!msg.json)
                .lastIndexOf(true);

              if (lastModelMessageIndex !== -1) {
                const msgToUpdate = newHistory[lastModelMessageIndex];
                try {
                  const parsedJson = JSON.parse(msgToUpdate.json!);
                  if (
                    Array.isArray(parsedJson) &&
                    draggingPointIndex < parsedJson.length
                  ) {
                    parsedJson[draggingPointIndex].point = [
                      finalPointCoords.y * 1000,
                      finalPointCoords.x * 1000,
                    ];
                    newHistory[lastModelMessageIndex] = {
                      ...msgToUpdate,
                      json: JSON.stringify(parsedJson),
                    };
                  }
                } catch (error) {
                  console.error(
                    'Fehler beim Aktualisieren des JSON im Chatverlauf:',
                    error,
                  );
                }
              }
              return newHistory;
            });
            setDraggingPointIndex(null);
          }
          if (dragging2DBox) {
            (e.target as HTMLElement).releasePointerCapture(e.pointerId);
            
            const finalBox = boundingBoxes2D[dragging2DBox.index];
            if (finalBox) {
                setChatHistory((prevHistory) => {
                  const newHistory = [...prevHistory];
                  const lastModelMessageIndex = newHistory
                    .map((msg) => msg.role === 'model' && !!msg.json)
                    .lastIndexOf(true);
                  if (lastModelMessageIndex !== -1) {
                    const msgToUpdate = newHistory[lastModelMessageIndex];
                    try {
                      const parsedJson = JSON.parse(msgToUpdate.json!);
                      if (Array.isArray(parsedJson) && dragging2DBox.index < parsedJson.length) {
                         const ymin = finalBox.y * 1000;
                         const xmin = finalBox.x * 1000;
                         const ymax = (finalBox.y + finalBox.height) * 1000;
                         const xmax = (finalBox.x + finalBox.width) * 1000;
                         parsedJson[dragging2DBox.index].box_2d = [ymin, xmin, ymax, xmax];
                         newHistory[lastModelMessageIndex] = {
                           ...msgToUpdate,
                           json: JSON.stringify(parsedJson),
                         };
                      }
                    } catch (error) {
                      console.error('Error updating JSON in chat history:', error);
                    }
                  }
                  return newHistory;
                });
            }
            setDragging2DBox(null);
            dragStartRef.current = null;
          }
        }}
        style={{
          width: boundingBoxContainer.width,
          height: boundingBoxContainer.height,
        }}>
        {lines.length > 0 && (
          <svg
            className="absolute top-0 left-0 w-full h-full"
            style={{
              pointerEvents: 'none',
              width: boundingBoxContainer?.width,
              height: boundingBoxContainer?.height,
            }}>
            {lines.map(([points, color], i) => (
              <path
                key={i}
                d={getSvgPathFromStroke(
                  getStroke(
                    points.map(([x, y]) => [
                      x * boundingBoxContainer!.width,
                      y * boundingBoxContainer!.height,
                      0.5,
                    ]),
                    lineOptions,
                  ),
                )}
                fill={color}
              />
            ))}
          </svg>
        )}
        {detectType === '2D-Begrenzungsrahmen' &&
          boundingBoxes2D.map((box, i) => {
            if (!box) return null;
            const isActive = activeResultIndex === i;
            const isDragging = dragging2DBox?.index === i;
            return (
              <div
                key={i}
                data-index={i}
                onPointerDown={(e) => {
                  if (detectType !== '2D-Begrenzungsrahmen') return;
                  e.stopPropagation();
                  (e.currentTarget as HTMLElement).setPointerCapture(e.pointerId);
                  const parentBounds = boundingBoxContainerRef.current!.getBoundingClientRect();
                  dragStartRef.current = {
                    x: (e.clientX - parentBounds.left) / boundingBoxContainer.width,
                    y: (e.clientY - parentBounds.top) / boundingBoxContainer.height,
                    box: boundingBoxes2D[i]!,
                  };
                  setDragging2DBox({index: i, handle: 'body'});
                  setActiveResultIndex(i);
                }}
                className={`absolute bbox border-2 ${isActive ? 'active-box z-20' : i === hoveredBox ? 'border-transparent z-10' : 'border-[#3B68FF]'} ${i === hoveredBox ? 'reveal' : ''} ${isDragging ? 'grabbing dragging-box' : 'cursor-grab'}`}
                style={{
                  transformOrigin: '0 0',
                  top: box.y * 100 + '%',
                  left: box.x * 100 + '%',
                  width: box.width * 100 + '%',
                  height: box.height * 100 + '%',
                }}>
                <div
                  className={`absolute left-0 top-0 w-6 h-6 text-white text-sm flex items-center justify-center font-bold ${isActive || i === hoveredBox ? 'bg-[var(--accent-color)]' : 'bg-[#3B68FF]'}`}>
                  {i + 1}
                </div>
                {(isActive || i === hoveredBox) && (
                  <>
                    <button
                      className="refine-button"
                      title="Bereich genauer analysieren"
                      onPointerDown={(e) => {
                        e.stopPropagation();
                        setRefineBoxRequest({index: i, box});
                      }}
                      style={{
                        position: 'absolute',
                        top: '4px',
                        right: '4px',
                        zIndex: 35,
                      }}>
                      🔍
                    </button>
                    {(['tl', 'tr', 'bl', 'br'] as const).map(handle => (
                       <div
                         key={handle}
                         onPointerDown={(e) => {
                            if (detectType !== '2D-Begrenzungsrahmen') return;
                           e.stopPropagation();
                           (e.currentTarget.parentElement as HTMLElement).setPointerCapture(e.pointerId);
                            const parentBounds = boundingBoxContainerRef.current!.getBoundingClientRect();
                            dragStartRef.current = {
                              x: (e.clientX - parentBounds.left) / boundingBoxContainer.width,
                              y: (e.clientY - parentBounds.top) / boundingBoxContainer.height,
                              box: boundingBoxes2D[i]!,
                            };
                           setDragging2DBox({index: i, handle});
                           setActiveResultIndex(i);
                         }}
                         className={`resize-handle resize-handle-${handle}`}
                       />
                    ))}
                  </>
                )}
              </div>
            );
          })}
        {detectType === 'Segmentierungsmasken' &&
          boundingBoxMasks.map((box, i) => {
            if (!box) return null;
            const isActive = activeResultIndex === i;
            return (
              <div
                key={i}
                data-index={i}
                onClick={() => {
                  setActiveResultIndex((prev) => (prev === i ? null : i));
                }}
                className={`absolute bbox cursor-pointer border-2 ${isActive ? 'active-box z-20' : i === hoveredBox ? 'border-[var(--accent-color)] z-10' : 'border-[#3B68FF]'} ${i === hoveredBox ? 'reveal' : ''}`}
                style={{
                  transformOrigin: '0 0',
                  top: box.y * 100 + '%',
                  left: box.x * 100 + '%',
                  width: box.width * 100 + '%',
                  height: box.height * 100 + '%',
                }}>
                <BoxMask
                  box={box}
                  index={i}
                  isHovered={i === hoveredBox || isActive}
                />
                <div
                  className={`absolute left-0 top-0 w-6 h-6 text-white text-sm flex items-center justify-center font-bold z-10 ${isActive || i === hoveredBox ? 'bg-[var(--accent-color)]' : 'bg-[#3B68FF]'}`}>
                  {i + 1}
                </div>
              </div>
            );
          })}

        {detectType === 'Punkte' &&
          points.map((point, i) => {
            if (!point) return null;
            const isActive = activeResultIndex === i;
            const isDragging = i === draggingPointIndex;
            const color =
              isActive || i === hoveredBox
                ? 'var(--accent-color)'
                : labelColorMap[point.label] || '#3B68FF';
            return (
              <div
                key={i}
                data-index={i}
                onPointerDown={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  (e.currentTarget as HTMLElement).setPointerCapture(
                    e.pointerId,
                  );
                  setDraggingPointIndex(i);
                  setActiveResultIndex(i);
                }}
                className={`absolute bbox ${isDragging ? 'grabbing' : 'cursor-pointer'}`}
                style={{
                  left: `${point.point.x * 100}%`,
                  top: `${point.point.y * 100}%`,
                  zIndex: isDragging ? 30 : isActive ? 20 : i === hoveredBox ? 10 : 1,
                }}>
                <div
                  className="absolute text-center text-white text-xs px-2 py-0.5 bottom-5 rounded-sm -translate-x-1/2 left-1/2"
                  style={{backgroundColor: color}}>
                  {i + 1}
                </div>
                <div
                  className={`absolute w-3 h-3 rounded-full border-white border -translate-x-1/2 -translate-y-1/2 ${isActive ? 'active-box' : ''}`}
                  style={{
                    backgroundColor: color,
                    transform: `translate(-50%, -50%) scale(${isActive || i === hoveredBox ? 1.5 : 1})`,
                    transition: isDragging
                      ? 'none'
                      : 'transform 0.15s ease-in-out, border-color 0.15s ease-in-out',
                    borderWidth: isActive ? '3px' : '1px',
                    borderColor: isActive ? 'var(--accent-color)' : 'white',
                  }}></div>
              </div>
            );
          })}
      </div>
    </div>
  );
}

function BoxMask({
  box,
  index,
  isHovered,
}: {
  box: {
    x: number;
    y: number;
    width: number;
    height: number;
    label: string;
    imageData: string;
  };
  index: number;
  isHovered: boolean;
}) {
  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const rgb = segmentationColorsRgb[index % segmentationColorsRgb.length];

  useEffect(() => {
    if (canvasRef.current) {
      const ctx = canvasRef.current.getContext('2d');
      if (ctx) {
        const image = new Image();
        image.src = box.imageData;
        image.onload = () => {
          canvasRef.current!.width = image.width;
          canvasRef.current!.height = image.height;
          ctx.imageSmoothingEnabled = false;
          ctx.drawImage(image, 0, 0);
          const pixels = ctx.getImageData(0, 0, image.width, image.height);
          const data = pixels.data;
          for (let i = 0; i < data.length; i += 4) {
            // alpha from mask
            data[i + 3] = data[i];
            // color from palette
            data[i] = rgb[0];
            data[i + 1] = rgb[1];
            data[i + 2] = rgb[2];
          }
          ctx.putImageData(pixels, 0, 0);
        };
      }
    }
  }, [canvasRef, box.imageData, rgb]);

  return (
    <canvas
      ref={canvasRef}
      className="absolute top-0 left-0 w-full h-full transition-opacity duration-150"
      style={{opacity: isHovered ? 0.75 : 0.5}}
    />
  );
}