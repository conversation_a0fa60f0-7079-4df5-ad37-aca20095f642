/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
/* tslint:disable */
// Copyright 2024 Google LLC

// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at

//     https://www.apache.org/licenses/LICENSE-2.0

// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import {useAtom} from 'jotai';
import type {SetStateAction} from 'jotai';
import {
  ActiveResultIndexAtom,
  BoundingBoxes2DAtom,
  BoundingBoxMasksAtom,
  BumpSessionAtom,
  ChatHistoryAtom,
  ActiveChatSessionAtom,
  ImageSentAtom,
  ImageSrcAtom,
  IsEsp32ImageAtom,
  IsFetchingEsp32ImageAtom,
  IsUploadedImageAtom,
  LabelColorMapAtom,
  LinesAtom,
  MediaDimensionsAtom,
  MoreDetailsRequestAtom,
  PointsAtom,
  ShareStream,
  StreamTypeAtom,
  IsSnapshotAtom,
  DefaultExampleImagesAtom,
  SystemPromptAtom,
  defaultSystemPrompt,
  LastInitialPromptAtom,
  DraggingPointIndexAtom,
  Dragging2DBoxAtom,
  LoadingMessageAtom,
  DetectTypeAtom,
  RefinePointRequestAtom,
  RefineBoxRequestAtom,
  LastAnalysisTypeAtom,
} from './atoms';
import {
  BoundingBox2DType,
  BoundingBoxMaskType,
  ChatMessage,
  DetectTypes,
  MediaDimensions,
  PointType,
} from './Types';
import {AIChat} from './providers/types';

export function useResetState() {
  const [, setImageSent] = useAtom(ImageSentAtom) as [
    any,
    (u: SetStateAction<boolean>) => void,
  ];
  const [, setBoundingBoxes2D] = useAtom(BoundingBoxes2DAtom) as [
    any,
    (u: SetStateAction<(BoundingBox2DType | null)[]>) => void,
  ];
  const [, setBoundingBoxMasks] = useAtom(BoundingBoxMasksAtom) as [
    any,
    (u: SetStateAction<(BoundingBoxMaskType | null)[]>) => void,
  ];
  const [, setPoints] = useAtom(PointsAtom) as [
    any,
    (u: SetStateAction<(PointType | null)[]>) => void,
  ];
  const [, setBumpSession] = useAtom(BumpSessionAtom) as [
    any,
    (u: SetStateAction<number>) => void,
  ];
  const [, setImageSrc] = useAtom(ImageSrcAtom) as [
    any,
    (u: SetStateAction<string | null>) => void,
  ];
  const [, setIsUploadedImage] = useAtom(IsUploadedImageAtom) as [
    any,
    (u: SetStateAction<boolean>) => void,
  ];
  const [, setStream] = useAtom(ShareStream) as [
    any,
    (u: SetStateAction<MediaStream | null>) => void,
  ];
  const [, setStreamType] = useAtom(StreamTypeAtom) as [
    any,
    (u: SetStateAction<'webcam' | 'screenshare' | null>) => void,
  ];
  const [, setLines] = useAtom(LinesAtom) as [
    any,
    (u: SetStateAction<[[number, number][], string][]>) => void,
  ];
  const [, setIsEsp32Image] = useAtom(IsEsp32ImageAtom) as [
    any,
    (u: SetStateAction<boolean>) => void,
  ];
  const [, setIsFetchingEsp32Image] = useAtom(IsFetchingEsp32ImageAtom) as [
    any,
    (u: SetStateAction<boolean>) => void,
  ];
  const [, setActiveChatSession] = useAtom(ActiveChatSessionAtom) as [
    any,
    (u: SetStateAction<AIChat | null>) => void,
  ];
  const [, setChatHistory] = useAtom(ChatHistoryAtom) as [
    any,
    (u: SetStateAction<ChatMessage[]>) => void,
  ];
  const [, setLabelColorMap] = useAtom(LabelColorMapAtom) as [
    any,
    (u: SetStateAction<Record<string, string>>) => void,
  ];
  const [, setMediaDimensions] = useAtom(MediaDimensionsAtom) as [
    any,
    (u: SetStateAction<MediaDimensions>) => void,
  ];
  const [, setIsSnapshot] = useAtom(IsSnapshotAtom) as [
    any,
    (u: SetStateAction<boolean>) => void,
  ];
  const [defaultImages] = useAtom(DefaultExampleImagesAtom);
  const [, setActiveResultIndex] = useAtom(ActiveResultIndexAtom) as [
    any,
    (u: SetStateAction<number | null>) => void,
  ];
  const [, setMoreDetailsRequest] = useAtom(MoreDetailsRequestAtom) as [
    any,
    (u: SetStateAction<{index: number; label: string} | null>) => void,
  ];
  const [, setDraggingPointIndex] = useAtom(DraggingPointIndexAtom) as [
    any,
    (u: SetStateAction<number | null>) => void,
  ];
  const [, setDragging2DBox] = useAtom(Dragging2DBoxAtom) as [
    any,
    (
      u: SetStateAction<{
        index: number;
        handle: 'body' | 'tl' | 'tr' | 'bl' | 'br';
      } | null>,
    ) => void,
  ];
  const [, setLoadingMessage] = useAtom(LoadingMessageAtom) as [
    any,
    (u: SetStateAction<string>) => void,
  ];
  const [, setDetectType] = useAtom(DetectTypeAtom) as [
    any,
    (u: SetStateAction<DetectTypes | null>) => void,
  ];
  const [, setLastAnalysisType] = useAtom(LastAnalysisTypeAtom) as [
    any,
    (u: SetStateAction<DetectTypes | null>) => void,
  ];
  const [, setRefinePointRequest] = useAtom(RefinePointRequestAtom) as [
    any,
    (
      u: SetStateAction<{
        index: number;
        label: string;
        point: PointType['point'];
      } | null>,
    ) => void,
  ];
  const [, setRefineBoxRequest] = useAtom(RefineBoxRequestAtom) as [
    any,
    (u: SetStateAction<{index: number; box: BoundingBox2DType} | null>) => void,
  ];

  return () => {
    setImageSent(false);
    setBoundingBoxes2D([]);
    setBoundingBoxMasks([]);
    setPoints([]);
    setLines([]);
    setBumpSession((prev) => prev + 1);
    // Reset to default image or clear image
    setImageSrc(defaultImages[0] ?? null);
    setIsUploadedImage(false);
    setStream(null);
    setStreamType(null);
    setIsEsp32Image(false);
    setIsFetchingEsp32Image(false);
    setActiveChatSession(null);
    setChatHistory([]);
    setLabelColorMap({});
    setMediaDimensions({width: 1, height: 1});
    setIsSnapshot(false);
    setActiveResultIndex(null);
    setMoreDetailsRequest(null);
    setDraggingPointIndex(null);
    setDragging2DBox(null);
    setLoadingMessage('');
    setDetectType(null);
    setLastAnalysisType(null);
    setRefinePointRequest(null);
    setRefineBoxRequest(null);
  };
}
export function useResetAnalysisState() {
  const [, setImageSent] = useAtom(ImageSentAtom) as [
    any,
    (u: SetStateAction<boolean>) => void,
  ];
  const [, setBoundingBoxes2D] = useAtom(BoundingBoxes2DAtom) as [
    any,
    (u: SetStateAction<(BoundingBox2DType | null)[]>) => void,
  ];
  const [, setBoundingBoxMasks] = useAtom(BoundingBoxMasksAtom) as [
    any,
    (u: SetStateAction<(BoundingBoxMaskType | null)[]>) => void,
  ];
  const [, setPoints] = useAtom(PointsAtom) as [
    any,
    (u: SetStateAction<(PointType | null)[]>) => void,
  ];
  const [, setLines] = useAtom(LinesAtom) as [
    any,
    (u: SetStateAction<[[number, number][], string][]>) => void,
  ];
  const [, setActiveChatSession] = useAtom(ActiveChatSessionAtom) as [
    any,
    (u: SetStateAction<AIChat | null>) => void,
  ];
  const [, setChatHistory] = useAtom(ChatHistoryAtom) as [
    any,
    (u: SetStateAction<ChatMessage[]>) => void,
  ];
  const [, setLabelColorMap] = useAtom(LabelColorMapAtom) as [
    any,
    (u: SetStateAction<Record<string, string>>) => void,
  ];
  const [, setLastInitialPrompt] = useAtom(LastInitialPromptAtom) as [
    any,
    (u: SetStateAction<string>) => void,
  ];
  const [, setActiveResultIndex] = useAtom(ActiveResultIndexAtom) as [
    any,
    (u: SetStateAction<number | null>) => void,
  ];
  const [, setMoreDetailsRequest] = useAtom(MoreDetailsRequestAtom) as [
    any,
    (u: SetStateAction<{index: number; label: string} | null>) => void,
  ];
  const [, setDraggingPointIndex] = useAtom(DraggingPointIndexAtom) as [
    any,
    (u: SetStateAction<number | null>) => void,
  ];
  const [, setDragging2DBox] = useAtom(Dragging2DBoxAtom) as [
    any,
    (
      u: SetStateAction<{
        index: number;
        handle: 'body' | 'tl' | 'tr' | 'bl' | 'br';
      } | null>,
    ) => void,
  ];
  const [, setLoadingMessage] = useAtom(LoadingMessageAtom) as [
    any,
    (u: SetStateAction<string>) => void,
  ];
  const [, setDetectType] = useAtom(DetectTypeAtom) as [
    any,
    (u: SetStateAction<DetectTypes | null>) => void,
  ];
  const [, setLastAnalysisType] = useAtom(LastAnalysisTypeAtom) as [
    any,
    (u: SetStateAction<DetectTypes | null>) => void,
  ];
  const [, setRefinePointRequest] = useAtom(RefinePointRequestAtom) as [
    any,
    (
      u: SetStateAction<{
        index: number;
        label: string;
        point: PointType['point'];
      } | null>,
    ) => void,
  ];
  const [, setRefineBoxRequest] = useAtom(RefineBoxRequestAtom) as [
    any,
    (u: SetStateAction<{index: number; box: BoundingBox2DType} | null>) => void,
  ];

  return () => {
    setImageSent(false);
    setBoundingBoxes2D([]);
    setBoundingBoxMasks([]);
    setPoints([]);
    setLines([]);
    setActiveChatSession(null);
    setChatHistory([]);
    setLabelColorMap({});
    // Keep last initial prompt for retry functionality
    // setLastInitialPrompt('');
    setActiveResultIndex(null);
    setMoreDetailsRequest(null);
    setDraggingPointIndex(null);
    setDragging2DBox(null);
    setLoadingMessage('');
    // Keep detect type for retry functionality
    // setDetectType(null);
    setLastAnalysisType(null);
    setRefinePointRequest(null);
    setRefineBoxRequest(null);
  };
}