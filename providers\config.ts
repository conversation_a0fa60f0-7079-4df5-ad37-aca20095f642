/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
// Copyright 2024 Google LLC

// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at

//     https://www.apache.org/licenses/LICENSE-2.0

// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import {ProviderConfig} from './types';

// This file defines the available AI providers for the application.
// When moving to a local environment with a Vite proxy,
// change the `baseUrl` for 'openrouter' to your proxy path, e.g., '/api/openrouter'.

export const availableProviderConfigs: ProviderConfig[] = [
  {
    id: 'gemini',
    name: '<PERSON> (Cloud)',
    type: 'gemini',
    // In AI Studio, process.env.API_KEY is automatically populated.
    // For local development, set GEMINI_API_KEY in your .env file.
    apiKey: process.env.API_KEY || process.env.GEMINI_API_KEY || '',
    model: 'gemini-2.5-pro', // Upgraded to Pro for better accuracy
  },
  {
    id: 'openrouter',
    name: 'OpenRouter (Horizon Beta)',
    type: 'openrouter',
    // For local development, set OPENROUTER_API_KEY in your .env file.
    apiKey: process.env.OPENROUTER_API_KEY || '',
    model: 'openrouter/horizon-beta',
    // Using proxy path to avoid CORS issues in local development
    baseUrl: '/api/openrouter',
  },
];
