/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
/* tslint:disable */
// Copyright 2024 Google LLC

// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at

//     https://www.apache.org/licenses/LICENSE-2.0

// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import {useAtom, useAtomValue} from 'jotai';
import type {SetStateAction} from 'jotai';
import getStroke from 'perfect-freehand';
import {useState, useRef, useEffect} from 'react';
import {
  BoundingBoxMasksAtom,
  BoundingBoxes2DAtom,
  DetectTypeAtom,
  ImageSrcAtom,
  LinesAtom,
  PointsAtom,
  PromptsAtom,
  ShareStream,
  TemperatureAtom,
  VideoRefAtom,
  ChatHistoryAtom,
  ActiveChatSessionAtom,
  LabelColorMapAtom,
  MediaDimensionsAtom,
  LastInitialPromptAtom,
  SystemPromptAtom,
  HoverEnteredAtom,
  WebSearchModeAtom,
  MoreDetailsRequestAtom,
  LoadingMessageAtom,
  RefinePointRequestAtom,
  RefineBoxRequestAtom,
  AnalysisResolutionAtom,
  LastAnalysisTypeAtom,
  ActiveProviderAtom,
} from './atoms';
import {lineOptions, pointColors} from './consts';
import {getSvgPathFromStroke, loadImage, hasVisualKeywords} from './utils';
import {
  ChatMessage,
  BoundingBox2DType,
  BoundingBoxMaskType,
  MediaDimensions,
  PointType,
  DetectTypes,
} from './Types';
import {useResetAnalysisState} from './hooks';
import {AIChat} from './providers/types';

// Helper function to check for valid JSON strings that are objects or arrays
const isJsonString = (str: string) => {
  if (typeof str !== 'string' || !str.trim()) {
    return false;
  }
  const trimmedStr = str.trim();
  if (
    (trimmedStr.startsWith('{') && trimmedStr.endsWith('}')) ||
    (trimmedStr.startsWith('[') && trimmedStr.endsWith(']'))
  ) {
    try {
      JSON.parse(trimmedStr);
      return true;
    } catch (e) {
      return false;
    }
  }
  return false;
};

const analysisTools: {label: string; type: DetectTypes}[] = [
  {label: '2D-Rahmen', type: '2D-Begrenzungsrahmen'},
  {label: 'Masken', type: 'Segmentierungsmasken'},
  {label: 'Punkte', type: 'Punkte'},
];

export function Prompt() {
  const [temperature] = useAtom(TemperatureAtom);
  const [boundingBoxes2D, setBoundingBoxes2D] = useAtom(BoundingBoxes2DAtom) as [
    (BoundingBox2DType | null)[],
    (u: SetStateAction<(BoundingBox2DType | null)[]>) => void,
  ];
  const [boundingBoxMasks, setBoundingBoxMasks] = useAtom(
    BoundingBoxMasksAtom,
  ) as [
    (BoundingBoxMaskType | null)[],
    (u: SetStateAction<(BoundingBoxMaskType | null)[]>) => void,
  ];
  const [stream] = useAtom(ShareStream);
  const [detectType, setDetectType] = useAtom(DetectTypeAtom) as [
    DetectTypes | null,
    (u: SetStateAction<DetectTypes | null>) => void,
  ];
  const [points, setPoints] = useAtom(PointsAtom) as [
    (PointType | null)[],
    (u: SetStateAction<(PointType | null)[]>) => void,
  ];
  const [, setHoverEntered] = useAtom(HoverEnteredAtom) as [
    any,
    (u: SetStateAction<boolean>) => void,
  ];
  const [lines] = useAtom(LinesAtom);
  const [videoRef] = useAtom(VideoRefAtom);
  const [imageSrc] = useAtom(ImageSrcAtom);
  const [isLoading, setIsLoading] = useState(false);
  const [loadingMessage, setLoadingMessage] = useAtom(LoadingMessageAtom) as [
    string,
    (u: SetStateAction<string>) => void,
  ];
  const [prompt, setPrompt] = useState('');
  const [lastInitialPrompt, setLastInitialPrompt] = useAtom(
    LastInitialPromptAtom,
  ) as [string, (u: SetStateAction<string>) => void];

  const [prompts] = useAtom(PromptsAtom);
  const [chatHistory, setChatHistory] = useAtom(ChatHistoryAtom) as [
    ChatMessage[],
    (u: SetStateAction<ChatMessage[]>) => void,
  ];
  const [activeChatSession, setActiveChatSession] = useAtom(
    ActiveChatSessionAtom,
  ) as [AIChat | null, (u: SetStateAction<AIChat | null>) => void];
  const activeProvider = useAtomValue(ActiveProviderAtom);
  const [, setLabelColorMap] = useAtom(LabelColorMapAtom) as [
    any,
    (u: SetStateAction<Record<string, string>>) => void,
  ];
  const [expandedJsonIndices, setExpandedJsonIndices] = useState(
    new Set<number>(),
  );
  const [activeMediaDimensions, setActiveMediaDimensions] =
    useAtom(MediaDimensionsAtom) as [
      MediaDimensions,
      (u: SetStateAction<MediaDimensions>) => void,
    ];
  const systemInstruction = useAtomValue(SystemPromptAtom);
  const resetAnalysis = useResetAnalysisState();
  const [webSearchMode, setWebSearchMode] = useAtom(WebSearchModeAtom) as [
    boolean,
    (u: SetStateAction<boolean>) => void,
  ];
  const [moreDetailsRequest, setMoreDetailsRequest] = useAtom(
    MoreDetailsRequestAtom,
  ) as [
    {index: number; label: string} | null,
    (u: SetStateAction<{index: number; label: string} | null>) => void,
  ];
  const [refinePointRequest, setRefinePointRequest] = useAtom(
    RefinePointRequestAtom,
  ) as [
    {index: number; label: string; point: PointType['point']} | null,
    (
      u: SetStateAction<{
        index: number;
        label: string;
        point: PointType['point'];
      } | null>,
    ) => void,
  ];
  const [refineBoxRequest, setRefineBoxRequest] = useAtom(
    RefineBoxRequestAtom,
  ) as [
    {index: number; box: BoundingBox2DType} | null,
    (u: SetStateAction<{index: number; box: BoundingBox2DType} | null>) => void,
  ];
  const [analysisResolution] = useAtom(AnalysisResolutionAtom);
  const [, setLastAnalysisType] = useAtom(LastAnalysisTypeAtom) as [
    unknown,
    (u: SetStateAction<DetectTypes | null>) => void,
  ];

  const chatContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (activeProvider && !activeChatSession) {
      const session = activeProvider.createChat({systemInstruction});
      setActiveChatSession(session);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeProvider, activeChatSession, systemInstruction]);

  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop =
        chatContainerRef.current.scrollHeight;
    }
  }, [chatHistory, isLoading]);

  useEffect(() => {
    if (refinePointRequest) {
      handleRefinePoint(refinePointRequest);
      setRefinePointRequest(null);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [refinePointRequest]);

  useEffect(() => {
    if (refineBoxRequest) {
      handleRefineBox(refineBoxRequest);
      setRefineBoxRequest(null);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [refineBoxRequest]);

  const handleToggleJson = (index: number) => {
    setExpandedJsonIndices((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(index)) {
        newSet.delete(index);
      } else {
        newSet.add(index);
      }
      return newSet;
    });
  };

  async function handleMoreDetails(request: {index: number; label: string}) {
    if (isLoading) {
      console.warn('Anfrage ignoriert, eine andere Operation läuft bereits.');
      return;
    }
    const prompt = `Beschreibe das Objekt #${request.index + 1} (${
      request.label
    }) genauer, basierend auf dem, was du bereits gesehen hast. Konzentriere dich auf visuelle Merkmale.`;
    handleTextSend(prompt, `Mehr Details zu #${request.index + 1}`);
  }

  useEffect(() => {
    if (moreDetailsRequest) {
      handleMoreDetails(moreDetailsRequest);
      setMoreDetailsRequest(null);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [moreDetailsRequest]);

  async function handleSend(currentPrompt: string) {
    if (isLoading || !currentPrompt.trim()) return;

    if (detectType) {
      // It's an analysis request
      await handleAnalysisSend(currentPrompt);
    } else {
      // It's a regular chat or web search request
      await handleTextSend(currentPrompt);
    }
  }

  async function handleRefineBox(request: {
    index: number;
    box: BoundingBox2DType;
  }) {
    if (isLoading || !imageSrc || !activeProvider.supportsVision) return;

    setIsLoading(true);
    setLoadingMessage(
      `Analysiere Ausschnitt für Objekt #${request.index + 1}...`,
    );

    const userMessage: ChatMessage = {
      role: 'user',
      text: `Analysiere den ausgewählten Bereich für Objekt #${
        request.index + 1
      } ("${request.box.label}") genauer.`,
    };
    setChatHistory((p) => [...p, userMessage]);

    try {
      const image = await loadImage(imageSrc);
      const {width: originalWidth, height: originalHeight} = image;
      const {box} = request;

      const sx = box.x * originalWidth;
      const sy = box.y * originalHeight;
      const sWidth = box.width * originalWidth;
      const sHeight = box.height * originalHeight;

      if (sWidth < 1 || sHeight < 1) {
        throw new Error('Der ausgewählte Bereich ist zu klein zum Analysieren.');
      }

      const cropCanvas = document.createElement('canvas');
      const cropCtx = cropCanvas.getContext('2d');
      if (!cropCtx) {
        throw new Error('Canvas-Kontext konnte nicht erstellt werden.');
      }

      const aspectRatio = sWidth / sHeight;
      let targetWidth, targetHeight;

      if (aspectRatio >= 1) {
        targetWidth = analysisResolution;
        targetHeight = analysisResolution / aspectRatio;
      } else {
        targetHeight = analysisResolution;
        targetWidth = analysisResolution * aspectRatio;
      }
      cropCanvas.width = Math.round(targetWidth);
      cropCanvas.height = Math.round(targetHeight);

      cropCtx.drawImage(
        image,
        sx,
        sy,
        sWidth,
        sHeight,
        0,
        0,
        cropCanvas.width,
        cropCanvas.height,
      );

      const dataUrl = cropCanvas.toDataURL('image/jpeg');

      const result = await activeProvider.generateContent({
        prompt:
          'Beschreibe den Inhalt dieses Bildausschnitts kurz und prägnant in 2-3 Sätzen.',
        image: dataUrl.replace('data:image/jpeg;base64,', ''),
        config: {
          systemInstruction:
            'Du bist ein hochpräziser visueller Analyse-Agent. Deine Aufgabe ist es, einen Bildausschnitt kurz und bündig zusammenzufassen.',
        },
      });

      const modelMessage: ChatMessage = {
        role: 'model',
        text: result.text,
      };
      setChatHistory((p) => [...p, modelMessage]);
    } catch (error) {
      console.error('Fehler bei der Detailanalyse:', error);
      const errorMessageText =
        error instanceof Error ? error.message : String(error);
      const errorMessage: ChatMessage = {
        role: 'model',
        text: `Entschuldigung, die Detailanalyse ist fehlgeschlagen: ${errorMessageText}`,
      };
      setChatHistory((p) => [...p, errorMessage]);
    } finally {
      setIsLoading(false);
      setLoadingMessage('');
    }
  }

  async function handleRefinePoint(request: {
    index: number;
    label: string;
    point: {x: number; y: number};
  }) {
    if (isLoading || !activeProvider.supportsVision) return;

    setIsLoading(true);
    setLoadingMessage(`Präzisiere Position für: "${request.label}"...`);

    const userMessage: ChatMessage = {
      role: 'user',
      text: `Präzisiere die Position für "${request.label}".`,
    };
    setChatHistory((p) => [...p, userMessage]);

    try {
      let activeDataURL = '';
      const maxSize = analysisResolution;
      const copyCanvas = document.createElement('canvas');
      const ctx = copyCanvas.getContext('2d')!;

      if (stream && videoRef.current) {
        const video = videoRef.current!;
        const scale = Math.min(
          maxSize / video.videoWidth,
          maxSize / video.videoHeight,
        );
        copyCanvas.width = video.videoWidth * scale;
        copyCanvas.height = video.videoHeight * scale;
        ctx.drawImage(video, 0, 0, copyCanvas.width, copyCanvas.height);
      } else if (imageSrc) {
        const image = await loadImage(imageSrc);
        const scale = Math.min(maxSize / image.width, maxSize / image.height);
        copyCanvas.width = image.width * scale;
        copyCanvas.height = image.height * scale;
        ctx.drawImage(image, 0, 0, copyCanvas.width, copyCanvas.height);
      } else {
        throw new Error('Keine Bildquelle für die Präzisierung verfügbar.');
      }
      activeDataURL = copyCanvas.toDataURL('image/png');

      const refinePrompt = `
Aufgabe: Präzise Neupositionierung eines Objekts.
Kontext: Du analysierst ein Bild. Eine frühere Analyse hat ein Objekt an einer ungefähren Position platziert. Diese Position ist nur ein Ausgangspunkt für deine Suche und kann ungenau sein.

Objekt zur Überprüfung:
- Objekt mit der Bezeichnung "${
        request.label
      }", das sich grob in der Nähe von [y=${request.point.y.toFixed(
        3,
      )}, x=${request.point.x.toFixed(3)}] befindet.

Deine Anweisung:
1.  Ignoriere die vorherige Position. Verlasse dich ausschließlich auf deine erneute visuelle Analyse des gesamten Bildes.
2.  Finde die **genaue zentrale Koordinate** für das oben genannte Objekt.
3.  Gib deine Antwort als **EINZIGES JSON-Objekt** im folgenden Format zurück. Absolut kein umschließender Text oder Markdown-Code-Fencing (wie \`\`\`json).

{"point": [y, x], "label": "${request.label}"}

WICHTIG:
- Die Koordinaten [y, x] müssen auf den Bereich 0-1000 normalisiert sein.
- Der "label"-Wert muss exakt mit dem von mir angegebenen Label übereinstimmen.
- Gib die exakteste Position an, die du finden kannst, auch wenn sie weit von der ursprünglichen Schätzung entfernt ist.
`;
      const result = await activeProvider.generateContent({
        prompt: refinePrompt,
        image: activeDataURL.replace('data:image/png;base64,', ''),
        config: {
          systemInstruction:
            'Du bist ein hochpräziser visueller Analyse-Agent. Deine einzige Aufgabe ist es, die exakten Koordinaten eines bestimmten Objekts zu finden und sie im angeforderten JSON-Format zurückzugeben.',
        },
      });

      let responseText = result.text.trim();
      const fenceRegex = /^```(?:json)?\s*\n?(.*?)\n?\s*```$/s;
      const match = responseText.match(fenceRegex);
      if (match && match[1]) {
        responseText = match[1].trim();
      }

      if (!isJsonString(responseText)) {
        throw new Error(`Die API hat kein gültiges JSON-Objekt zurückgegeben.`);
      }

      const item = JSON.parse(responseText);

      if (
        !item.point ||
        !Array.isArray(item.point) ||
        item.point.length !== 2 ||
        typeof item.label !== 'string' ||
        item.label !== request.label
      ) {
        throw new Error('Die API hat ein fehlerhaftes oder nicht übereinstimmendes JSON-Objekt zurückgegeben.');
      }

      const newPoint: PointType = {
        point: {
          x: item.point[1] / 1000,
          y: item.point[0] / 1000,
        },
        label: item.label,
      };

      setPoints((prev) =>
        prev.map((p, i) => (i === request.index ? newPoint : p)),
      );

      setChatHistory((prev) => {
        const newHistory = [...prev];
        const lastModelMessageIndex = newHistory
          .map((msg) => msg.role === 'model' && !!msg.json)
          .lastIndexOf(true);
        if (lastModelMessageIndex !== -1) {
          const msgToUpdate = newHistory[lastModelMessageIndex];
          try {
            const historyJson = JSON.parse(msgToUpdate.json!);
            if (
              Array.isArray(historyJson) &&
              request.index < historyJson.length
            ) {
              historyJson[request.index] = item;
              msgToUpdate.json = JSON.stringify(historyJson, null, 2);
            }
          } catch (e) {
            console.error('Fehler beim Aktualisieren des Verlaufs-JSON', e);
          }
        }
        return newHistory;
      });

      const modelMessage: ChatMessage = {
        role: 'model',
        text: `Position für "${request.label}" wurde erfolgreich präzisiert.`,
      };
      setChatHistory((p) => [...p, modelMessage]);
    } catch (error) {
      console.error('Fehler bei der Präzisierung:', error);
      const errorMessageText =
        error instanceof Error ? error.message : String(error);
      const errorMessage: ChatMessage = {
        role: 'model',
        text: `Entschuldigung, die Präzisierung ist fehlgeschlagen: ${errorMessageText}`,
      };
      setChatHistory((p) => [...p, errorMessage]);
    } finally {
      setIsLoading(false);
      setLoadingMessage('');
    }
  }

  async function handleAnalysisSend(currentPrompt: string) {
    if (!activeProvider.supportsVision) {
      alert(
        `Der ausgewählte Provider "${activeProvider.name}" unterstützt keine Bildanalyse.`,
      );
      return;
    }
    if (!imageSrc && !stream) {
      alert(
        'Bitte laden Sie ein Bild hoch, geben Sie Ihren Bildschirm frei oder rufen Sie es zuerst von der ESP32-Kamera ab, um eine Analyse durchzuführen.',
      );
      return;
    }
    if (!detectType) return; // Should not happen if logic is correct

    setIsLoading(true);
    setLoadingMessage('Analyse wird gestartet...');
    setLastInitialPrompt(currentPrompt); // Save the prompt for retry
    const userMessage: ChatMessage = {
      role: 'user',
      text: `Analyse-Anfrage (${detectType}): ${currentPrompt}`,
    };
    setChatHistory((prev) => [...prev, userMessage]);
    setPrompt('');

    let activeDataURL = '';
    const maxSize = analysisResolution;
    const copyCanvas = document.createElement('canvas');
    const ctx = copyCanvas.getContext('2d')!;

    setLoadingMessage('Bild wird vorbereitet...');
    if (stream && videoRef.current) {
      const video = videoRef.current!;
      setActiveMediaDimensions({
        width: video.videoWidth,
        height: video.videoHeight,
      });
      const scale = Math.min(
        maxSize / video.videoWidth,
        maxSize / video.videoHeight,
      );
      copyCanvas.width = video.videoWidth * scale;
      copyCanvas.height = video.videoHeight * scale;
      ctx.drawImage(
        video,
        0,
        0,
        video.videoWidth * scale,
        video.videoHeight * scale,
      );
    } else if (imageSrc) {
      try {
        setLoadingMessage('Bild wird geladen...');
        const image = await loadImage(imageSrc);
        setActiveMediaDimensions({
          width: image.width,
          height: image.height,
        });
        const scale = Math.min(maxSize / image.width, maxSize / image.height);
        copyCanvas.width = image.width * scale;
        copyCanvas.height = image.height * scale;
        setLoadingMessage('Bild wird verarbeitet...');
        ctx.drawImage(image, 0, 0, image.width * scale, image.height * scale);
      } catch (error) {
        console.error('Fehler beim Laden des Bildes für die Leinwand:', error);
        alert(
          'Das Bild konnte nicht zur Verarbeitung geladen werden. Bitte versuchen Sie es mit einem anderen Bild.',
        );
        setIsLoading(false);
        setLoadingMessage('');
        return;
      }
    } else {
      alert('Keine Bildquelle verfügbar.');
      setIsLoading(false);
      setLoadingMessage('');
      return;
    }
    activeDataURL = copyCanvas.toDataURL('image/png');

    if (lines.length > 0) {
      setLoadingMessage('Anmerkungen werden eingezeichnet...');
      for (const line of lines) {
        const p = new Path2D(
          getSvgPathFromStroke(
            getStroke(
              line[0].map(([x, y]) => [
                x * copyCanvas.width,
                y * copyCanvas.height,
                0.5,
              ]),
              lineOptions,
            ),
          ),
        );
        ctx.fillStyle = line[1];
        ctx.fill(p);
      }
      activeDataURL = copyCanvas.toDataURL('image/png');
    }

    const finalPrompt = prompts[detectType].replace(
      'Gegenständen',
      currentPrompt,
    );
    setHoverEntered(false);

    try {
      if (!activeChatSession) {
        throw new Error(
          'Chat-Sitzung ist nicht aktiv. Bitte versuchen Sie es erneut.',
        );
      }

      setLoadingMessage('Warte auf Antwort vom KI-Modell...');
      // Using the provider's sendMessage, which can handle multimodal input
      const result = await activeChatSession.sendMessage({
        message: [
          {
            type: 'image',
            imageData: activeDataURL.replace('data:image/png;base64,', ''),
          },
          {type: 'text', text: finalPrompt},
        ],
      });

      setLoadingMessage('Ergebnisse werden verarbeitet...');
      let responseText = result.text;
      const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
      const match = responseText.match(fenceRegex);
      if (match && match[2]) {
        responseText = match[2].trim();
      }

      if (!isJsonString(responseText)) {
        const errorMessage = `Fehler: Die API-Antwort ist kein gültiges JSON. Antwort erhalten:\n\n"${responseText}"`;
        throw new Error(errorMessage);
      }

      const parsedJson = JSON.parse(responseText);
      const responseData = Array.isArray(parsedJson)
        ? parsedJson
        : [parsedJson];

      setLastAnalysisType(detectType);
      const summaryText = `Ich habe ${responseData.length} Objekt(e) gefunden und markiert. Sie können mir jetzt Folgefragen dazu stellen.`;
      const modelMessage: ChatMessage = {
        role: 'model',
        text: summaryText,
        json: responseText,
      };
      setChatHistory((prev) => [...prev, modelMessage]);

      if (detectType === '2D-Begrenzungsrahmen') {
        const formattedBoxes = responseData.map(
          (box: any) => {
            console.log('Processing box:', box);
            // Handle different box formats from different providers
            if (Array.isArray(box.box_2d)) {
              // Original Gemini format: [ymin, xmin, ymax, xmax]
              console.log('Using Array format');
              const [ymin, xmin, ymax, xmax] = box.box_2d;
              const finalBox = {
                x: xmin / 1000,
                y: ymin / 1000,
                width: Math.max(0.05, (xmax - xmin) / 1000), // Minimum 5% width
                height: Math.max(0.05, (ymax - ymin) / 1000), // Minimum 5% height
                label: box.label,
              };
              console.log('Array format result:', finalBox);
              return finalBox;
            } else if (box.box_2d && typeof box.box_2d.x !== 'undefined') {
              // OpenRouter format: {x, y, w, h}
              console.log('Using {x,y,w,h} format');
              const {x, y, w, h} = box.box_2d;
              const finalBox = {
                x: x / 1000,
                y: y / 1000,
                width: Math.max(0.05, w / 1000), // Minimum 5% width
                height: Math.max(0.05, h / 1000), // Minimum 5% height
                label: box.label,
              };
              console.log('{x,y,w,h} format result:', finalBox);
              return finalBox;
            } else if (box.box_2d && typeof box.box_2d.x_min !== 'undefined') {
              // OpenRouter/Gemini format: {x_min, y_min, x_max, y_max}
              console.log('Using {x_min,y_min,x_max,y_max} format');
              const {x_min, y_min, x_max, y_max} = box.box_2d;
              const imageWidth = 1000;  // Assumption for image width
              const imageHeight = 800;  // Assumption for image height
              const finalBox = {
                x: x_min / imageWidth,
                y: y_min / imageHeight,
                width: Math.max(0.05, (x_max - x_min) / imageWidth), // Minimum 5% width
                height: Math.max(0.05, (y_max - y_min) / imageHeight), // Minimum 5% height
                label: box.label,
              };
              console.log('{x_min,y_min,x_max,y_max} format result:', finalBox);
              return finalBox;
            } else {
              console.warn('Unknown box format:', box.box_2d);
              return null;
            }
          },
        ).filter(box => box !== null);

        setHoverEntered(false);
        setBoundingBoxes2D(formattedBoxes);
      } else if (detectType === 'Punkte') {
        setLabelColorMap((currentMap) => {
          const newMap = {...currentMap};
          const existingLabels = Object.keys(newMap);
          const newLabels = [
            ...new Set(responseData.map((p: any) => p.label)),
          ].filter((l) => !existingLabels.includes(l as string));
          newLabels.forEach((label) => {
            const nextColorIndex = Object.keys(newMap).length;
            newMap[label as string] =
              pointColors[nextColorIndex % pointColors.length];
          });
          return newMap;
        });

        const formattedPoints = responseData.map(
          (point: {point: [number, number]; label: string}) => {
            return {
              point: {
                x: point.point[1] / 1000,
                y: point.point[0] / 1000,
              },
              label: point.label,
            };
          },
        );
        setPoints(formattedPoints);
      } else if (detectType === 'Segmentierungsmasken') {
        const formattedBoxes = responseData.map(
          (box: {
            box_2d: [number, number, number, number];
            label: string;
            mask: string;
          }) => {
            const [ymin, xmin, ymax, xmax] = box.box_2d;
            return {
              x: xmin / 1000,
              y: ymin / 1000,
              width: (xmax - xmin) / 1000,
              height: (ymax - ymin) / 1000,
              label: box.label,
              imageData: box.mask,
            };
          },
        );
        setHoverEntered(false);
        const sortedBoxes = formattedBoxes.sort(
          (a: any, b: any) => b.width * b.height - a.width * a.height,
        );
        setBoundingBoxMasks(sortedBoxes);
      }
    } catch (error) {
      console.error(
        'Fehler beim Aufrufen der KI-API oder beim Parsen der Antwort:',
        error,
      );
      const errorMessage: ChatMessage = {
        role: 'model',
        text: `Fehler beim Aufrufen der KI-API oder beim Parsen der Antwort:\n${
          error instanceof Error ? error.message : String(error)
        }`,
      };
      setChatHistory((prev) => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
      setLoadingMessage('');
    }
  }

  async function handleTextSend(
    currentPrompt: string,
    displayPrompt?: string,
  ) {
    if (!activeChatSession) {
      alert('Der Chat wird noch initialisiert. Bitte warten Sie einen Moment.');
      return;
    }

    setIsLoading(true);
    setLoadingMessage('Warte auf Antwort vom KI-Modell...');
    const userMessage: ChatMessage = {
      role: 'user',
      text: displayPrompt || currentPrompt,
    };
    setChatHistory((prev) => [...prev, userMessage]);
    setPrompt('');

    try {
      if (webSearchMode && activeProvider.id === 'gemini') {
        // Handle web search request via one-off generateContent
        const result = await activeProvider.generateContent({
          prompt: currentPrompt,
          config: {
            tools: [{googleSearch: {}}],
          },
        });
        setWebSearchMode(false); // Turn off after use

        const groundingMetadata = (result.rawResponse as any)?.candidates?.[0]
          ?.groundingMetadata?.groundingChunks;
        const validGroundingMetadata = groundingMetadata
          ?.filter((chunk: any) => chunk.web?.uri)
          .map((chunk: any) => ({
            web: {
              uri: chunk.web.uri!,
              title: chunk.web.title || '',
            },
          }));
        const modelMessage: ChatMessage = {
          role: 'model',
          text: result.text,
          groundingMetadata: validGroundingMetadata,
        };
        setChatHistory((prev) => [...prev, modelMessage]);
      } else {
        // Handle normal follow-up chat
        let detectionContext = '';
        if (boundingBoxes2D.length > 0) {
          detectionContext = JSON.stringify(
            boundingBoxes2D.filter((b) => b),
            null,
            2,
          );
        } else if (boundingBoxMasks.length > 0) {
          detectionContext = JSON.stringify(
            boundingBoxMasks
              .filter((b) => b)
              .map((b) => ({...b, imageData: undefined})),
            null,
            2,
          );
        } else if (points.length > 0) {
          detectionContext = JSON.stringify(
            points.filter((p) => p),
            null,
            2,
          );
        }

        const isVisualQuery = hasVisualKeywords(currentPrompt);
        let contextualPrompt: string;
        if (
          isVisualQuery &&
          (boundingBoxes2D.length > 0 ||
            boundingBoxMasks.length > 0 ||
            points.length > 0)
        ) {
          contextualPrompt = `Dies ist eine visuelle Detailfrage. Konzentriere dich bei deiner Antwort stark auf die visuellen Eigenschaften aus dem Originalbild, das du bereits kennst. Frage: "${currentPrompt}"\n\nKontext der bereits erkannten Objekte:\n${detectionContext}\n\nAntworte ausführlich und im Gesprächston.`;
        } else if (
          boundingBoxes2D.length > 0 ||
          boundingBoxMasks.length > 0 ||
          points.length > 0
        ) {
          contextualPrompt = `Basierend auf dem Bild und den zuvor erkannten Objekten, beantworte bitte die folgende Frage: "${currentPrompt}"\n\nZur Erinnerung, hier ist der Kontext der Objekte, die im ersten Schritt erkannt wurden:\n${detectionContext}\n\nAntworte ausführlich und im Gesprächston.`;
        } else {
          contextualPrompt = currentPrompt;
        }

        const result = await activeChatSession!.sendMessage({
          message: contextualPrompt,
        });

        const modelMessage: ChatMessage = {role: 'model', text: result.text};
        setChatHistory((prev) => [...prev, modelMessage]);
      }
    } catch (error) {
      console.error('Fehler beim Senden der Folge-Nachricht:', error);
      const errorMessage: ChatMessage = {
        role: 'model',
        text: `Entschuldigung, es ist ein Fehler aufgetreten: ${
          error instanceof Error ? error.message : String(error)
        }`,
      };
      setChatHistory((prev) => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
      setLoadingMessage('');
    }
  }

  function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    handleSend(prompt);
  }

  const handleRetry = () => {
    if (!lastInitialPrompt || isLoading) return;
    // Reset detection-related state
    resetAnalysis();
    // Re-run the initial send with the last prompt
    handleAnalysisSend(lastInitialPrompt);
  };

  const handleResetAnalysis = () => {
    resetAnalysis();
  };

  const quickPrompts = [
    {
      label: 'Szene beschreiben',
      prompt: 'Beschreibe die gesamte Szene detaillierter.',
    },
    {
      label: 'Kontext geben',
      prompt:
        'Was für eine Art von Szene ist das? Was könnte hier passieren?',
    },
    {
      label: 'Probleme finden',
      prompt:
        'Gibt es auf dem Bild Probleme, Anomalien oder etwas Ungewöhnliches?',
    },
  ];

  const hasAnalysisResults =
    boundingBoxes2D.length > 0 ||
    boundingBoxMasks.length > 0 ||
    points.length > 0;

  return (
    <div className="flex grow flex-col gap-3 h-full">
      <div
        ref={chatContainerRef}
        className="flex-grow overflow-y-auto p-4 border rounded-md bg-gray-50 dark:bg-gray-800 space-y-4 flex flex-col">
        {chatHistory.length === 0 && !isLoading ? (
          <div className="m-auto text-center text-[var(--text-color-secondary)]">
            Stellen Sie eine Frage oder wählen Sie ein Analysewerkzeug,
            <br />
            um mit einem Bild zu beginnen.
          </div>
        ) : (
          <>
            {chatHistory.map((msg, index) => (
              <div
                key={index}
                className={`flex flex-col ${
                  msg.role === 'user' ? 'items-end' : 'items-start'
                }`}>
                <div
                  className={`flex items-start gap-2 ${
                    msg.role === 'user' ? 'flex-row-reverse' : 'flex-row'
                  }`}>
                  <div
                    className={`chat-bubble ${
                      msg.role === 'user' ? 'user-bubble' : 'model-bubble'
                    }`}>
                    <div className="whitespace-pre-wrap">{msg.text}</div>
                    {msg.json && isJsonString(msg.json) && (
                      <>
                        <button
                          onClick={() => handleToggleJson(index)}
                          className="p-0 border-none bg-transparent underline text-left w-full mt-2 text-xs"
                          style={{
                            color:
                              msg.role === 'user'
                                ? 'white'
                                : 'var(--accent-color)',
                            outline: 'none',
                          }}>
                          {expandedJsonIndices.has(index)
                            ? 'JSON ausblenden'
                            : 'JSON anzeigen'}
                        </button>
                        {expandedJsonIndices.has(index) && (
                          <pre className="whitespace-pre-wrap text-xs bg-gray-100 dark:bg-gray-900 text-[var(--text-color-primary)] p-2 rounded mt-2 overflow-x-auto">
                            {JSON.stringify(JSON.parse(msg.json), null, 2)}
                          </pre>
                        )}
                      </>
                    )}
                    {msg.groundingMetadata &&
                      msg.groundingMetadata.length > 0 && (
                        <div className="mt-3 pt-3 border-t border-gray-300 dark:border-gray-600">
                          <h4 className="text-xs font-bold mb-1">Quellen:</h4>
                          <ol className="list-decimal list-inside text-xs space-y-1">
                            {msg.groundingMetadata.map((meta, i) => (
                              <li key={i}>
                                <a
                                  href={meta.web.uri}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="source-link"
                                  title={meta.web.title}>
                                  {meta.web.title || meta.web.uri}
                                </a>
                              </li>
                            ))}
                          </ol>
                        </div>
                      )}
                  </div>
                </div>
              </div>
            ))}
            {isLoading && (
              <div className="flex flex-col items-start">
                <div className="flex items-start gap-2 flex-row">
                  <div className="chat-bubble model-bubble">
                    <div className="flex items-center gap-3">
                      <svg
                        className="animate-spin h-5 w-5 text-[var(--text-color-primary)]"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24">
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      <span className="whitespace-pre-wrap">
                        {loadingMessage}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>

      {hasAnalysisResults && (
        <div className="flex gap-2 justify-center">
          {quickPrompts.map((p) => (
            <button
              key={p.label}
              onClick={() => handleTextSend(p.prompt)}
              disabled={isLoading}
              className="quick-prompt-button"
              title={p.prompt}>
              {p.label}
            </button>
          ))}
        </div>
      )}

      <form onSubmit={handleSubmit} className="flex flex-col gap-3">
        <div className="flex items-center gap-2">
          <span className="text-sm font-bold text-[var(--text-color-secondary)]">
            Analysewerkzeuge:
          </span>
          {analysisTools.map((tool) => (
            <button
              key={tool.type}
              type="button"
              onClick={() =>
                setDetectType((prev) => (prev === tool.type ? null : tool.type))
              }
              className={`quick-prompt-button ${
                detectType === tool.type ? 'active-tool' : ''
              }`}
              disabled={isLoading || !activeProvider.supportsVision}>
              {tool.label}
            </button>
          ))}
        </div>
        <div className="flex gap-3 items-center">
          <textarea
            className="w-full bg-[var(--input-color)] rounded-lg resize-none p-4"
            placeholder={
              detectType
                ? `Was soll mit "${
                    analysisTools.find((t) => t.type === detectType)?.label
                  }" erkannt werden?`
                : 'Stellen Sie eine Frage...'
            }
            rows={1}
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSubmit(e);
              }
            }}
            disabled={isLoading}
            aria-label="Chat-Nachricht eingeben"
          />
          {hasAnalysisResults && (
            <div className="flex gap-2">
              <button
                type="button"
                onClick={handleResetAnalysis}
                disabled={isLoading}
                className="retry-button"
                title="Analyse zurücksetzen"
                aria-label="Analyse zurücksetzen">
                🧹
              </button>
              <button
                type="button"
                onClick={handleRetry}
                disabled={isLoading || !lastInitialPrompt}
                className="retry-button"
                title="Letzte Analyse wiederholen"
                aria-label="Analyse wiederholen">
                🔄
              </button>
            </div>
          )}
          <button
            type="submit"
            className="bg-[#3B68FF] !text-white !border-none flex items-center justify-center px-6"
            disabled={isLoading || !prompt.trim()}>
            {detectType ? 'Analysieren' : 'Senden'}
          </button>
        </div>
      </form>
    </div>
  );
}