/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
/* tslint:disable */
// Copyright 2024 Google LLC

// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at

//     https://www.apache.org/licenses/LICENSE-2.0

// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import {useAtom} from 'jotai';
import type {SetStateAction} from 'jotai';
import {useEffect, useRef} from 'react';
import {
  ActiveResultIndexAtom,
  BoundingBoxes2DAtom,
  BoundingBoxMasksAtom,
  ChatHistoryAtom,
  HoveredBoxAtom,
  MoreDetailsRequestAtom,
  PointsAtom,
  RefinePointRequestAtom,
  LastAnalysisTypeAtom,
} from './atoms';
import {
  BoundingBox2DType,
  BoundingBoxMaskType,
  ChatMessage,
  PointType,
} from './Types';

export function DetectionResults() {
  const [lastAnalysisType] = useAtom(LastAnalysisTypeAtom);
  const [boundingBoxes2D, setBoundingBoxes2D] = useAtom(BoundingBoxes2DAtom);
  const [boundingBoxMasks, setBoundingBoxMasks] = useAtom(BoundingBoxMasksAtom);
  const [points, setPoints] = useAtom(PointsAtom);

  const [hoveredBox, setHoveredBox] = useAtom(HoveredBoxAtom) as [
    number | null,
    (u: SetStateAction<number | null>) => void,
  ];
  const [, setChatHistory] = useAtom(ChatHistoryAtom) as [
    any,
    (u: SetStateAction<ChatMessage[]>) => void,
  ];
  const [activeResultIndex, setActiveResultIndex] = useAtom(
    ActiveResultIndexAtom,
  ) as [number | null, (u: SetStateAction<number | null>) => void];
  const [, setMoreDetailsRequest] = useAtom(MoreDetailsRequestAtom) as [
    any,
    (u: SetStateAction<{index: number; label: string} | null>) => void,
  ];
  const [, setRefinePointRequest] = useAtom(RefinePointRequestAtom) as [
    any,
    (
      u: SetStateAction<{
        index: number;
        label: string;
        point: PointType['point'];
      } | null>,
    ) => void,
  ];

  const itemRefs = useRef<(HTMLLIElement | null)[]>([]);

  const results = (() => {
    switch (lastAnalysisType) {
      case '2D-Begrenzungsrahmen':
        return boundingBoxes2D;
      case 'Segmentierungsmasken':
        return boundingBoxMasks;
      case 'Punkte':
        return points;
      default:
        return [];
    }
  })();

  useEffect(() => {
    if (activeResultIndex !== null && itemRefs.current[activeResultIndex]) {
      itemRefs.current[activeResultIndex]?.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest',
      });
    } else if (hoveredBox !== null && itemRefs.current[hoveredBox]) {
      itemRefs.current[hoveredBox]?.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest',
      });
    }
  }, [hoveredBox, activeResultIndex]);

  const handleDeleteItem = (indexToDelete: number) => {
    setHoveredBox(null);
    setActiveResultIndex(null);

    switch (lastAnalysisType) {
      case '2D-Begrenzungsrahmen':
        setBoundingBoxes2D((prev) =>
          prev.map((item, i) => (i === indexToDelete ? null : item)),
        );
        break;
      case 'Segmentierungsmasken':
        setBoundingBoxMasks((prev) =>
          prev.map((item, i) => (i === indexToDelete ? null : item)),
        );
        break;
      case 'Punkte':
        setPoints((prev) =>
          prev.map((item, i) => (i === indexToDelete ? null : item)),
        );
        break;
    }

    setChatHistory((prevHistory: ChatMessage[]) => {
      const newHistory = [...prevHistory];
      const lastModelMessageIndex = newHistory
        .map((msg) => msg.role === 'model' && !!msg.json)
        .lastIndexOf(true);

      if (lastModelMessageIndex !== -1) {
        const msgToUpdate = newHistory[lastModelMessageIndex];
        try {
          const parsedJson = JSON.parse(msgToUpdate.json!);
          if (Array.isArray(parsedJson) && indexToDelete < parsedJson.length) {
            parsedJson[indexToDelete] = null;
            newHistory[lastModelMessageIndex] = {
              ...msgToUpdate,
              json: JSON.stringify(parsedJson),
            };
          }
        } catch (e) {
          console.error('Error updating JSON in chat history:', e);
        }
      }
      return newHistory;
    });
  };

  const handleSelectItem = (index: number) => {
    setActiveResultIndex((prev) => (prev === index ? null : index));
  };

  const allResultsDeleted = results.every((item) => item === null);

  if (results.length === 0 || allResultsDeleted) {
    return (
      <div className="p-4 h-full flex items-center justify-center text-center text-[var(--text-color-secondary)]">
        Die Erkennungsergebnisse werden hier angezeigt.
      </div>
    );
  }

  return (
    <div className="p-4">
      <h3 className="font-bold uppercase text-sm mb-4">Erkannte Objekte</h3>
      <ul className="list-none p-0 m-0 space-y-1">
        {results.map((item, index) => {
          if (!item) {
            return null;
          }
          const isActive = activeResultIndex === index;
          const isPoint = lastAnalysisType === 'Punkte';
          return (
            <li
              key={index}
              ref={(el) => {
                itemRefs.current[index] = el;
              }}
              className={`result-item flex items-center p-2 rounded-md cursor-pointer transition-colors duration-150 ${
                isActive
                  ? 'bg-[var(--accent-color)] text-white'
                  : hoveredBox === index
                    ? 'bg-gray-200 dark:bg-gray-700'
                    : 'bg-transparent'
              }`}
              onMouseEnter={() => setHoveredBox(index)}
              onMouseLeave={() => setHoveredBox(null)}
              onClick={() => handleSelectItem(index)}>
              <div
                className={`flex-shrink-0 w-6 h-6 leading-6 text-center font-bold text-white rounded-full text-xs mt-px transition-colors duration-150 ${
                  isActive || hoveredBox === index
                    ? 'bg-white text-[var(--accent-color)]'
                    : 'bg-[#3B68FF]'
                }`}>
                {index + 1}
              </div>
              <span className="flex-grow break-words ml-3">{item.label}</span>
              <div className="flex items-center tool-buttons">
                {isPoint && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      const pointItem = item as PointType;
                      setRefinePointRequest({
                        index,
                        label: pointItem.label,
                        point: pointItem.point,
                      });
                    }}
                    className="tool-button"
                    title="Position präzisieren">
                    🎯
                  </button>
                )}
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setMoreDetailsRequest(() => ({index, label: item.label}));
                  }}
                  className="tool-button"
                  title="Mehr Details zu diesem Objekt">
                  ℹ️
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation(); // Prevent li's onMouseEnter from firing
                    handleDeleteItem(index);
                  }}
                  className="delete-item-button"
                  aria-label={`Entferne ${item.label}`}>
                  🗑️
                </button>
              </div>
            </li>
          );
        })}
      </ul>
    </div>
  );
}