/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
/* tslint:disable */
// Copyright 2024 Google LLC

// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at

//     https://www.apache.org/licenses/LICENSE-2.0

// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import {useAtom} from 'jotai';
import type {SetStateAction} from 'jotai';
import {useState, useEffect} from 'react';
import type {ChatMessage} from './Types';
import {
  SystemPromptAtom,
  ActiveChatSessionAtom,
  defaultSystemPrompt,
  SystemPromptCollapsedAtom,
  ChatHistoryAtom,
} from './atoms';
import {AIChat} from './providers/types';

export function SystemPromptEditor() {
  const [systemPrompt, setSystemPrompt] = useAtom(SystemPromptAtom);
  const [localPrompt, setLocalPrompt] = useState(systemPrompt);
  const [activeChatSession, setActiveChatSession] = useAtom(
    ActiveChatSessionAtom,
  ) as [AIChat | null, (u: SetStateAction<AIChat | null>) => void];
  const [, setChatHistory] = useAtom(ChatHistoryAtom) as [
    any,
    (u: SetStateAction<ChatMessage[]>) => void,
  ];
  const [isCollapsed, setIsCollapsed] = useAtom(SystemPromptCollapsedAtom);

  useEffect(() => {
    setLocalPrompt(systemPrompt);
  }, [systemPrompt]);

  const handleApply = () => {
    if (localPrompt !== systemPrompt) {
      setSystemPrompt(localPrompt);
      // Changing the system prompt requires resetting the chat session.
      setActiveChatSession(null);
      setChatHistory([]);
    }
  };

  const handleReset = () => {
    setLocalPrompt(defaultSystemPrompt);
  };

  const hasUnappliedChanges = localPrompt !== systemPrompt;

  return (
    <div className="flex flex-col gap-2 border rounded-lg p-3 bg-[var(--bg-color-secondary)]">
      <button
        onClick={() => setIsCollapsed(!isCollapsed)}
        className="flex justify-between items-center w-full bg-transparent border-none p-0 text-left"
        aria-expanded={!isCollapsed}
        aria-controls="system-prompt-content">
        <label
          id="system-prompt-label"
          className="uppercase text-sm font-bold cursor-pointer">
          System-Prompt
        </label>
        <span className="text-lg">{isCollapsed ? '🔽' : '🔼'}</span>
      </button>

      {!isCollapsed && (
        <div
          id="system-prompt-content"
          className="flex flex-col gap-2 mt-2"
          role="region"
          aria-labelledby="system-prompt-label">
          <textarea
            id="system-prompt"
            rows={6}
            className="w-full bg-[var(--input-color)] rounded-lg resize-y p-2 text-sm font-mono"
            value={localPrompt}
            onChange={(e) => setLocalPrompt(e.target.value)}
            aria-label="System-Prompt bearbeiten"
          />
          {activeChatSession && hasUnappliedChanges && (
            <p className="text-xs text-[var(--text-color-secondary)]">
              Das Anwenden von Änderungen setzt die aktuelle Chat-Sitzung zurück.
            </p>
          )}
          <div className="flex gap-2 justify-end">
            <button
              onClick={handleReset}
              disabled={localPrompt === defaultSystemPrompt}
              className="text-sm secondary !py-1 !px-3">
              Zurücksetzen
            </button>
            <button
              onClick={handleApply}
              disabled={!hasUnappliedChanges}
              className="text-sm !py-1 !px-3">
              Anwenden
            </button>
          </div>
        </div>
      )}
    </div>
  );
}