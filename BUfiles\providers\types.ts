/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
// Copyright 2024 Google LLC

// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at

//     https://www.apache.org/licenses/LICENSE-2.0

// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

/**
 * Provider abstraction types for AI services
 */

export interface MultimodalContent {
  type: 'text' | 'image';
  text?: string;
  imageUrl?: string;
  imageData?: string; // base64 encoded
}

export interface GenerateContentParams {
  prompt?: string;
  image?: string; // base64 encoded
  config?: {
    systemInstruction?: string;
    temperature?: number;
    maxOutputTokens?: number;
    tools?: any[];
  };
}

export interface GenerateContentResult {
  text: string;
  rawResponse?: any; // To store provider-specific response data if needed
}

export interface SendMessageParams {
  message: string | MultimodalContent[];
}

export interface MessageResult {
  text: string;
  rawResponse?: any;
}

export interface ChatConfig {
  systemInstruction?: string;
  model?: string;
  temperature?: number;
}

export interface AIChat {
  sendMessage(params: SendMessageParams): Promise<MessageResult>;
}

export interface AIProvider {
  id: string;
  name: string;
  generateContent(params: GenerateContentParams): Promise<GenerateContentResult>;
  createChat(config: ChatConfig): AIChat;
  supportsVision: boolean;
}

export interface ProviderConfig {
  id: string;
  name: string;
  type: 'gemini' | 'openrouter';
  apiKey: string;
  baseUrl?: string;
  model?: string;
}

export class ProviderError extends Error {
  constructor(
    message: string,
    public provider: string,
    public code?: string,
    public statusCode?: number,
  ) {
    super(`[${provider} Error] ${message}`);
    this.name = 'ProviderError';
  }
}
