@import url("https://fonts.googleapis.com/css2?family=Space+Mono:ital,wght@0,400;0,700;1,400;1,700&family=Inter:wght@400;500;700&display=swap");

:root {
  --font-sans: "Inter", sans-serif;
  --font-mono: "Space Mono", monospace;
  --bg-color: #121212;
  --bg-color-secondary: #1e1e1e;
  --border-color: #333;
  --input-color: #2a2a2a;
  --text-color-primary: #ffffff;
  --text-color-secondary: #aaaaaa;
  --accent-color: #6366f1;
}

html:not(.dark) {
  --bg-color: #ffffff;
  --bg-color-secondary: #f3f4f6;
  --border-color: #e5e7eb;
  --input-color: #f9fafb;
  --text-color-primary: #111827;
  --text-color-secondary: #6b7280;
}

body {
  background-color: var(--bg-color);
  color: var(--text-color-primary);
  font-family: var(--font-sans);
}

#root {
  height: 100vh;
}

button,
textarea,
input[type="text"],
input[type="number"] {
  border-radius: 8px;
  border: 1px solid var(--border-color);
  padding: 10px 16px;
  background-color: transparent;
  color: var(--text-color-primary);
  font-family: inherit;
  font-size: 1rem;
  transition: all 0.2s ease-in-out;
  outline-color: var(--accent-color);
}

textarea {
  line-height: 1.5;
}

button {
  cursor: pointer;
  min-height: 52px;
}
button:not(:disabled):hover {
  border-color: var(--accent-color);
}
button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

button.secondary {
  background-color: var(--bg-color-secondary);
  border-color: var(--border-color);
}

button.secondary:not(:disabled):hover {
  background-color: var(--border-color);
}

.retry-button {
  background: var(--bg-color-secondary);
  border: 1px solid var(--border-color);
  padding: 0;
  width: 52px;
  height: 52px;
  flex-shrink: 0;
  font-size: 1.5rem;
}

.retry-button:hover:not(:disabled) {
  background-color: var(--border-color);
}

.chat-bubble {
  max-width: 85%;
  padding: 12px 16px;
  border-radius: 16px;
  word-break: break-word;
}

.user-bubble {
  background-color: var(--accent-color);
  color: white;
  border-bottom-right-radius: 4px;
}

.model-bubble {
  background-color: var(--bg-color-secondary);
  border: 1px solid var(--border-color);
  border-bottom-left-radius: 4px;
}

.result-item .delete-item-button,
.result-item .tool-button {
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.2s ease-in-out, visibility 0.2s ease-in-out;
  background: none;
  border: none;
  cursor: pointer;
  color: inherit;
  font-size: 1.25rem;
  padding: 0 0.25rem;
  line-height: 1;
  min-height: auto;
}

.result-item:hover .delete-item-button,
.result-item:hover .tool-button {
  visibility: visible;
  opacity: 1;
}

.result-item .tool-buttons {
  display: flex;
  gap: 4px;
}

.delete-item-button:hover,
.tool-button:hover {
  opacity: 0.7;
}

.hide-box:not(:hover) .bbox:not(.reveal) {
  opacity: 0;
}
.bbox {
  transition: all 0.15s ease-in-out;
}

.active-box {
  border-color: var(--accent-color) !important;
  border-width: 3px !important;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(99, 102, 241, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
  }
}

input[type="range"] {
  accent-color: var(--accent-color);
}

/* Custom Toggle Switch */
.toggle-checkbox:checked {
  right: 0;
  border-color: var(--accent-color);
  transform: translateX(100%);
}
.toggle-checkbox:checked + .toggle-label {
  background-color: var(--accent-color);
}
.toggle-checkbox {
  transition: all 0.2s ease-in-out;
  transform: translateX(0);
}

.source-link {
  text-decoration: underline;
  color: var(--accent-color);
  opacity: 0.8;
  transition: opacity 0.2s;
}

.source-link:hover {
  opacity: 1;
}

.quick-prompt-button {
  background-color: var(--bg-color-secondary);
  border: 1px solid var(--border-color);
  padding: 6px 12px;
  font-size: 0.875rem;
  min-height: auto;
  border-radius: 999px;
}

.quick-prompt-button:hover:not(:disabled) {
  background-color: var(--border-color);
  border-color: var(--accent-color);
}

.quick-prompt-button.active-tool {
  background-color: var(--accent-color);
  color: white;
  border-color: var(--accent-color);
}

.grabbing {
  cursor: grabbing !important;
}

.dragging-box {
  border-style: dashed;
}

.resize-handle {
  position: absolute;
  width: 12px;
  height: 12px;
  background: white;
  border: 2px solid var(--accent-color);
  border-radius: 50%;
  z-index: 30; /* Ensure handles are on top */
}

.resize-handle-tl {
  top: -6px;
  left: -6px;
  cursor: nwse-resize;
}
.resize-handle-tr {
  top: -6px;
  right: -6px;
  cursor: nesw-resize;
}
.resize-handle-bl {
  bottom: -6px;
  left: -6px;
  cursor: nesw-resize;
}
.resize-handle-br {
  bottom: -6px;
  right: -6px;
  cursor: nwse-resize;
}

.refine-button {
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: 1px solid white;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  min-height: auto;
  cursor: pointer;
  transition: background 0.2s;
  font-size: 1rem;
}

.refine-button:hover {
  background: rgba(0, 0, 0, 0.8);
  border-color: var(--accent-color);
}

.signal-control span {
  color: var(--accent-color);
  font-weight: 500;
}

.signal-control input {
  border-color: var(--accent-color);
}