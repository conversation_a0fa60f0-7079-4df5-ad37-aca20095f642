/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
/* tslint:disable */
// Copyright 2024 Google LLC

// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at

//     https://www.apache.org/licenses/LICENSE-2.0

// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import {useAtom, useAtomValue} from 'jotai';
import type {SetStateAction} from 'jotai';
import {useState, useEffect} from 'react';
import {
  DetectTypeAtom,
  HoverEnteredAtom,
  AnalysisResolutionAtom,
  ResultsPanelOpenAtom,
  RevealOnHoverModeAtom,
  WebSearchModeAtom,
  AvailableProvidersAtom,
  ActiveProviderIdAtom,
  ActiveProviderAtom,
} from './atoms';
import {useResetState} from './hooks';

export function TopBar() {
  const resetState = useResetState();
  const [revealOnHover, setRevealOnHoverMode] = useAtom(RevealOnHoverModeAtom) as [
    boolean,
    (u: SetStateAction<boolean>) => void,
  ];
  const [detectType] = useAtom(DetectTypeAtom);
  const [, setHoverEntered] = useAtom(HoverEnteredAtom) as [
    any,
    (u: SetStateAction<boolean>) => void,
  ];
  const [resultsPanelOpen, setResultsPanelOpen] = useAtom(ResultsPanelOpenAtom) as [
    boolean,
    (u: SetStateAction<boolean>) => void,
  ];
  const [webSearchMode, setWebSearchMode] = useAtom(WebSearchModeAtom) as [
    boolean,
    (u: SetStateAction<boolean>) => void,
  ];
  const [globalAnalysisResolution, setGlobalAnalysisResolution] =
    useAtom(AnalysisResolutionAtom);
  const [localAnalysisResolution, setLocalAnalysisResolution] = useState(
    String(globalAnalysisResolution),
  );
  const [availableProviders] = useAtom(AvailableProvidersAtom);
  const [activeProviderId, setActiveProviderId] = useAtom(ActiveProviderIdAtom);
  const activeProvider = useAtomValue(ActiveProviderAtom);

  useEffect(() => {
    setLocalAnalysisResolution(String(globalAnalysisResolution));
  }, [globalAnalysisResolution]);

  const handleResolutionBlur = () => {
    let finalValue = parseInt(localAnalysisResolution, 10);
    if (isNaN(finalValue)) {
      finalValue = globalAnalysisResolution;
    } else if (finalValue < 256) {
      finalValue = 256;
    } else if (finalValue > 800) {
      finalValue = 800;
    }
    setLocalAnalysisResolution(String(finalValue));
    setGlobalAnalysisResolution(finalValue);
  };

  const handleResolutionKeyDown = (
    e: React.KeyboardEvent<HTMLInputElement>,
  ) => {
    if (e.key === 'Enter') {
      handleResolutionBlur();
      e.currentTarget.blur();
    }
  };

  return (
    <>
      <div className="flex w-full items-center px-3 py-2 border-b justify-between">
        <div className="flex gap-3 items-center">
          <button
            onClick={() => {
              resetState();
            }}
            className="!p-0 !border-none underline bg-transparent"
            style={{
              minHeight: '0',
            }}>
            <div>Sitzung zurücksetzen</div>
          </button>
        </div>
        <div className="flex gap-4 items-center">
          <label className="flex items-center gap-2 px-3 select-none whitespace-nowrap border-l">
            <span className="text-sm">Modell</span>
            <select
              value={activeProviderId}
              onChange={(e) => setActiveProviderId(e.target.value)}
              className="p-1 text-sm bg-[var(--input-color)] border rounded-md"
              title="KI-Modell auswählen">
              {availableProviders.map((provider) => (
                <option key={provider.id} value={provider.id}>
                  {provider.name}
                </option>
              ))}
            </select>
          </label>

          {(detectType === '2D-Begrenzungsrahmen' ||
            detectType === 'Segmentierungsmasken') && (
            <label className="flex items-center gap-2 px-3 select-none whitespace-nowrap">
              <input
                type="checkbox"
                checked={revealOnHover}
                onChange={(e) => {
                  if (e.target.checked) {
                    setHoverEntered(false);
                  }
                  setRevealOnHoverMode(e.target.checked);
                }}
              />
              <div>Bei Hover anzeigen</div>
            </label>
          )}

          <label className="signal-control flex items-center gap-2 px-3 select-none whitespace-nowrap border-l">
            <span className="text-sm">Analyse-Auflösung</span>
            <input
              type="number"
              value={localAnalysisResolution}
              onChange={(e) => setLocalAnalysisResolution(e.target.value)}
              onBlur={handleResolutionBlur}
              onKeyDown={handleResolutionKeyDown}
              step="128"
              min="256"
              max="800"
              className="w-20 p-1 text-sm bg-[var(--input-color)]"
              title="Auflösung für die Bildanalyse (in Pixel)"
            />
          </label>

          <label
            className="flex items-center gap-2 px-3 select-none whitespace-nowrap border-l"
            style={{
              opacity: activeProvider.id !== 'gemini' ? 0.5 : 1,
              cursor: activeProvider.id !== 'gemini' ? 'not-allowed' : '',
            }}>
            <span className="text-sm">Web-Recherche</span>
            <div className="relative inline-block w-10 align-middle select-none transition duration-200 ease-in">
              <input
                type="checkbox"
                name="toggle-web"
                id="toggle-web"
                className="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
                checked={webSearchMode}
                onChange={() =>
                  activeProvider.id === 'gemini' &&
                  setWebSearchMode(!webSearchMode)
                }
                disabled={activeProvider.id !== 'gemini'}
              />
              <label
                htmlFor="toggle-web"
                className="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 dark:bg-gray-600"
                style={{
                  cursor:
                    activeProvider.id !== 'gemini' ? 'not-allowed' : 'pointer',
                }}></label>
            </div>
          </label>

          <div className="flex items-center gap-2 px-3 select-none whitespace-nowrap border-l">
            <label
              htmlFor="toggle-results"
              className="cursor-pointer text-sm"
              aria-label={
                resultsPanelOpen
                  ? 'Ergebnis-Seitenleiste ausblenden'
                  : 'Ergebnis-Seitenleiste anzeigen'
              }>
              Ergebnisse
            </label>
            <div className="relative inline-block w-10 align-middle select-none transition duration-200 ease-in">
              <input
                type="checkbox"
                name="toggle-results"
                id="toggle-results"
                className="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
                checked={resultsPanelOpen}
                onChange={() => setResultsPanelOpen(!resultsPanelOpen)}
              />
              <label
                htmlFor="toggle-results"
                className="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 dark:bg-gray-600 cursor-pointer"></label>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}