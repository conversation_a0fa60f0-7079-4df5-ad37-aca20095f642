/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
/* tslint:disable */
// Copyright 2024 Google LLC

// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at

//     https://www.apache.org/licenses/LICENSE-2.0

// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import {useAtom} from 'jotai';
import type {SetStateAction} from 'jotai';
import {useEffect} from 'react';
import {Content} from './Content';
import {ExampleImages} from './ExampleImages';
import {ExtraModeControls} from './ExtraModeControls';
import {Prompt} from './Prompt';
import {SideControls} from './SideControls';
import {TopBar} from './TopBar';
import {
  BumpSessionAtom,
  ImageSrcAtom,
  InitFinishedAtom,
  IsUploadedImageAtom,
  ResultsPanelOpenAtom,
  DefaultExampleImagesAtom,
  ExampleGalleryImagesAtom,
} from './atoms';
import {useResetState} from './hooks';
import {DetectionResults} from './DetectionResults';
import {imageOptionsPromise} from './consts';
import {CameraSelectionModal} from './CameraSelectionModal';
import {SystemPromptEditor} from './SystemPromptEditor';

function App() {
  const [, setImageSrc] = useAtom(ImageSrcAtom) as [
    any,
    (u: SetStateAction<string | null>) => void,
  ];
  const resetState = useResetState();
  const [initFinished, setInitFinished] = useAtom(InitFinishedAtom) as [
    boolean,
    (u: SetStateAction<boolean>) => void,
  ];
  const [, setBumpSession] = useAtom(BumpSessionAtom);
  const [, setIsUploadedImage] = useAtom(IsUploadedImageAtom);
  const [resultsPanelOpen] = useAtom(ResultsPanelOpenAtom);
  const [, setDefaultExampleImages] = useAtom(DefaultExampleImagesAtom) as [
    any,
    (u: SetStateAction<string[]>) => void,
  ];
  const [, setExampleGalleryImages] = useAtom(ExampleGalleryImagesAtom) as [
    any,
    (u: SetStateAction<string[]>) => void,
  ];

  useEffect(() => {
    if (!window.matchMedia('(prefers-color-scheme: dark)').matches) {
      document.documentElement.classList.remove('dark');
    }
    // This ensures the content is rendered after the app has initialized.
    if (!initFinished) {
      setInitFinished(true);
    }
  }, [initFinished]);

  useEffect(() => {
    imageOptionsPromise.then((images) => {
      setDefaultExampleImages(images);
      setExampleGalleryImages(images);
      setImageSrc(images[0]);
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className="flex flex-col h-[100dvh]">
      <CameraSelectionModal />
      <div className="flex grow flex-col border-b overflow-hidden">
        <TopBar />
        <div className="flex grow overflow-hidden">
          <div className="grow relative">
            {initFinished ? <Content /> : null}
            <div className="absolute bottom-0 left-0 w-full z-10">
              <ExtraModeControls />
            </div>
          </div>
          <div
            className={`flex-shrink-0 border-l overflow-hidden transition-all duration-300 ease-in-out ${
              resultsPanelOpen ? 'w-[350px]' : 'w-0 border-transparent'
            }`}>
            <div className="h-full w-[350px] overflow-y-auto">
              <DetectionResults />
            </div>
          </div>
        </div>
      </div>
      <div className="flex shrink-0 w-full overflow-auto py-6 px-5 gap-6 lg:items-start h-[280px]">
        <div className="flex flex-col lg:flex-col gap-6 items-center border-r pr-5">
          <ExampleImages />
          <SideControls />
        </div>
        <div className="flex flex-col gap-4 grow h-full">
          <SystemPromptEditor />
          <Prompt />
        </div>
      </div>
    </div>
  );
}

export default App;