/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
/* tslint:disable */
// Copyright 2024 Google LLC

// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at

//     https://www.apache.org/licenses/LICENSE-2.0

// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import {atom} from 'jotai';
import {
  BoundingBox2DType,
  BoundingBoxMaskType,
  ChatMessage,
  DetectTypes,
  MediaDimensions,
  PointType,
} from './Types';
import {defaultPrompts} from './consts';
import {
  AIProvider,
  AIChat,
  ProviderConfig,
} from './providers/types';
import {ProviderFactory} from './providers/factory';
import {availableProviderConfigs} from './providers/config';

export const ImageSrcAtom = atom<string | null>(null);
export const IsUploadedImageAtom = atom(false);
export const IsEsp32ImageAtom = atom(false);
export const Esp32EndpointAtom = atom<string>('');
export const IsFetchingEsp32ImageAtom = atom(false);
export const IsSnapshotAtom = atom(false);

export const ImageSentAtom = atom(false);
export const BoundingBoxes2DAtom = atom<(BoundingBox2DType | null)[]>([]);
export const BoundingBoxMasksAtom = atom<(BoundingBoxMaskType | null)[]>([]);
export const PointsAtom = atom<(PointType | null)[]>([]);
export const LinesAtom = atom<[[number, number][], string][]>([]);
export const ActiveColorAtom = atom('rgb(213, 40, 40)');
export const DrawModeAtom = atom(false);

export const InitFinishedAtom = atom(false);
export const BumpSessionAtom = atom(0);

export const DetectTypeAtom = atom<DetectTypes | null>(null);
export const LastAnalysisTypeAtom = atom<DetectTypes | null>(null);
export const TemperatureAtom = atom(0);

export const HoveredBoxAtom = atom<number | null>(null);
export const ActiveResultIndexAtom = atom<number | null>(null);
export const DraggingPointIndexAtom = atom<number | null>(null);
export const Dragging2DBoxAtom = atom<{
  index: number;
  handle: 'body' | 'tl' | 'tr' | 'bl' | 'br';
} | null>(null);
export const MoreDetailsRequestAtom = atom<{index: number; label: string} | null>(
  null,
);
export const RefinePointRequestAtom = atom<{
  index: number;
  label: string;
  point: PointType['point'];
} | null>(null);
export const RefineBoxRequestAtom =
  atom<{index: number; box: BoundingBox2DType} | null>(null);
export const AnalysisResolutionAtom = atom<number>(640);

export const RevealOnHoverModeAtom = atom(true);
export const HoverEnteredAtom = atom(false);
export const ShareStream = atom<MediaStream | null>(null);
export const StreamTypeAtom = atom<'webcam' | 'screenshare' | null>(null);
export const VideoRefAtom = atom<{current: HTMLVideoElement | null}>({
  current: null,
});
export const PromptsAtom = atom(defaultPrompts);
export const ResultsPanelOpenAtom = atom(true);

export const ShowCameraSelectionAtom = atom(false);
export const AvailableCamerasAtom = atom<MediaDeviceInfo[]>([]);

// --- New Provider Architecture Atoms ---
export const AvailableProvidersAtom = atom<ProviderConfig[]>(
  availableProviderConfigs,
);
export const ActiveProviderIdAtom = atom<string>('gemini'); // Default to gemini for AI Studio

export const ActiveProviderAtom = atom<AIProvider>((get) => {
  const providers = get(AvailableProvidersAtom);
  const activeId = get(ActiveProviderIdAtom);
  const activeConfig = providers.find((p) => p.id === activeId);
  if (!activeConfig) {
    // Fallback to the first available provider if the active one is not found
    return ProviderFactory.createProvider(providers[0]);
  }
  return ProviderFactory.createProvider(activeConfig);
});

export const ActiveChatSessionAtom = atom<AIChat | null>(null);
export const ChatHistoryAtom = atom<ChatMessage[]>([]);
export const LabelColorMapAtom = atom<Record<string, string>>({});
export const MediaDimensionsAtom = atom<MediaDimensions>({width: 1, height: 1});

export const LastInitialPromptAtom = atom<string>('');
export const ExampleGalleryImagesAtom = atom<string[]>([]);
export const DefaultExampleImagesAtom = atom<string[]>([]);

export const defaultSystemPrompt = `Du bist ein KI-Kamera-Assistent. Deine Hauptaufgabe ist die Analyse von Bildern. Bei der ersten Anfrage des Benutzers identifizierst du Objekte und gibst das Ergebnis als reines JSON zurück. Anschließend beantwortest du Folgefragen des Benutzers zu dem Bild und den von dir erkannten Objekten. Sei in deinen Antworten auf Folgefragen gesprächig, hilfsbereit und beziehe dich klar auf die zuvor erkannten Objekte. Du kannst auch allgemeine Fragen ohne Bildkontext beantworten.`;
export const SystemPromptAtom = atom<string>(defaultSystemPrompt);
export const SystemPromptCollapsedAtom = atom(true);

export const WebSearchModeAtom = atom(false);
export const LoadingMessageAtom = atom<string>('');
