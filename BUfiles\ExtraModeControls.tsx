/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
/* tslint:disable */
// Copyright 2024 Google LLC

// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at

//     https://www.apache.org/licenses/LICENSE-2.0

// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Fix: Import SetStateAction for explicit typing of the Jotai atom setter.
import {useAtom} from 'jotai';
import type {SetStateAction} from 'jotai';
import {
  BoundingBoxMasksAtom,
  BoundingBoxes2DAtom,
  ChatHistoryAtom,
  ActiveChatSessionAtom,
  DrawModeAtom,
  ExampleGalleryImagesAtom,
  HoveredBoxAtom,
  ImageSrcAtom,
  IsSnapshotAtom,
  IsUploadedImageAtom,
  LabelColorMapAtom,
  LinesAtom,
  PointsAtom,
  ShareStream,
  StreamTypeAtom,
  VideoRefAtom,
} from './atoms';
import {useResetState} from './hooks';
import {Palette} from './Palette';
import {
  BoundingBox2DType,
  BoundingBoxMaskType,
  ChatMessage,
  PointType,
} from './Types';
import {AIChat} from './providers/types';

export function ExtraModeControls() {
  const [stream, setStream] = useAtom(ShareStream) as [
    MediaStream | null,
    (u: SetStateAction<MediaStream | null>) => void,
  ];
  const [, setHoveredBox] = useAtom(HoveredBoxAtom) as [
    any,
    (u: SetStateAction<number | null>) => void,
  ];
  const [drawMode, setDrawMode] = useAtom(DrawModeAtom) as [
    boolean,
    (u: SetStateAction<boolean>) => void,
  ];
  const [, setLines] = useAtom(LinesAtom) as [
    any,
    (u: SetStateAction<[[number, number][], string][]>) => void,
  ];
  const [, setStreamType] = useAtom(StreamTypeAtom) as [
    any,
    (u: SetStateAction<'webcam' | 'screenshare' | null>) => void,
  ];
  const [videoRef] = useAtom(VideoRefAtom);
  const [imageSrc, setImageSrc] = useAtom(ImageSrcAtom) as [
    string | null,
    (u: SetStateAction<string | null>) => void,
  ];
  const resetState = useResetState();
  const [isSnapshot, setIsSnapshot] = useAtom(IsSnapshotAtom) as [
    boolean,
    (u: SetStateAction<boolean>) => void,
  ];
  const [isUploadedImage, setIsUploadedImage] = useAtom(IsUploadedImageAtom) as [
    boolean,
    (u: SetStateAction<boolean>) => void,
  ];
  const [galleryImages, setGalleryImages] = useAtom(ExampleGalleryImagesAtom) as [
    string[],
    (u: SetStateAction<string[]>) => void,
  ];

  // Add setters for manual reset in handleSnapshot
  const [, setBoundingBoxes2D] = useAtom(BoundingBoxes2DAtom) as [
    any,
    (u: SetStateAction<(BoundingBox2DType | null)[]>) => void,
  ];
  const [, setBoundingBoxMasks] = useAtom(BoundingBoxMasksAtom) as [
    any,
    (u: SetStateAction<(BoundingBoxMaskType | null)[]>) => void,
  ];
  const [, setPoints] = useAtom(PointsAtom) as [
    any,
    (u: SetStateAction<(PointType | null)[]>) => void,
  ];
  const [, setActiveChatSession] = useAtom(ActiveChatSessionAtom) as [
    any,
    (u: SetStateAction<AIChat | null>) => void,
  ];
  const [, setChatHistory] = useAtom(ChatHistoryAtom) as [
    any,
    (u: SetStateAction<ChatMessage[]>) => void,
  ];
  const [, setLabelColorMap] = useAtom(LabelColorMapAtom) as [
    any,
    (u: SetStateAction<Record<string, string>>) => void,
  ];

  const handleSnapshot = () => {
    if (videoRef.current) {
      const video = videoRef.current;
      const canvas = document.createElement('canvas');
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
        const dataUrl = canvas.toDataURL('image/jpeg');

        // Stop stream before resetting state
        if (stream) {
          stream.getTracks().forEach((track) => track.stop());
        }

        // --- Manual, partial state reset for snapshot transition ---
        // This replaces the incorrect call to resetState()
        setBoundingBoxes2D([]);
        setBoundingBoxMasks([]);
        setPoints([]);
        setLines([]);
        setActiveChatSession(null);
        setChatHistory([]);
        setLabelColorMap({});
        setStream(null);
        setStreamType(null);

        // --- Set the new state for the snapshot ---
        setImageSrc(dataUrl);
        setIsSnapshot(true);
      }
    }
  };

  const handleStopStream = () => {
    if (stream) {
      stream.getTracks().forEach((track) => track.stop());
    }
    resetState();
  };

  const handleSaveImage = () => {
    if (!imageSrc) return;
    const link = document.createElement('a');
    link.href = imageSrc;
    link.download = 'schnappschuss.jpeg';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleAddToGallery = () => {
    if (imageSrc && !galleryImages.includes(imageSrc)) {
      setGalleryImages((prev) => [imageSrc, ...prev]);
    }
    setIsSnapshot(false);
    setIsUploadedImage(false);
  };

  if (drawMode) {
    return (
      <div className="flex gap-3 px-3 py-3 items-center justify-between border-t bg-[var(--bg-color)] bg-opacity-90">
        <div style={{width: 200}}></div>
        <div className="grow flex justify-center">
          <Palette />
        </div>
        <div className="flex gap-3">
          <div className="flex gap-3">
            <button
              className="flex gap-3 text-sm secondary"
              onClick={() => {
                setLines([]);
              }}>
              <div className="text-xs">🗑️</div>
              Löschen
            </button>
          </div>
          <div className="flex gap-3">
            <button
              className="flex gap-3 secondary"
              onClick={() => {
                setDrawMode(false);
              }}>
              <div className="text-sm">✅</div>
              <div>Fertig</div>
            </button>
          </div>
        </div>
      </div>
    );
  } else if (isSnapshot || isUploadedImage) {
    return (
      <div className="flex gap-3 px-3 py-3 border-t items-center justify-center bg-[var(--bg-color)] bg-opacity-90">
        {isSnapshot && (
          <button
            className="flex gap-3 text-sm items-center secondary"
            onClick={handleSaveImage}>
            <div className="text-xs">💾</div>
            <div className="whitespace-nowrap">Bild speichern</div>
          </button>
        )}
        <button
          className="flex gap-3 text-sm items-center secondary"
          onClick={handleAddToGallery}>
          <div className="text-xs">🖼️</div>
          <div className="whitespace-nowrap">Zur Galerie hinzufügen</div>
        </button>
      </div>
    );
  } else if (stream) {
    return (
      <div className="flex gap-3 px-3 py-3 border-t items-center justify-center bg-[var(--bg-color)] bg-opacity-90">
        <button
          className="flex gap-3 text-sm items-center secondary"
          onClick={handleSnapshot}>
          <div className="text-xs">📸</div>
          <div className="whitespace-nowrap">Schnappschuss machen</div>
        </button>
        <button
          className="flex gap-3 text-sm items-center secondary"
          onClick={handleStopStream}>
          <div className="text-xs">❌</div>
          <div className="whitespace-nowrap">Streaming beenden</div>
        </button>
      </div>
    );
  } else {
    return null;
  }
}
