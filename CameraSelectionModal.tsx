/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
/* tslint:disable */
// Copyright 2024 Google LLC

// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at

//     https://www.apache.org/licenses/LICENSE-2.0

// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import {useAtom} from 'jotai';
import type {SetStateAction} from 'jotai';
import {
  AvailableCamerasAtom,
  ShareStream,
  ShowCameraSelectionAtom,
  StreamTypeAtom,
} from './atoms';

export function CameraSelectionModal() {
  const [show, setShow] = useAtom(ShowCameraSelectionAtom) as [
    boolean,
    (u: SetStateAction<boolean>) => void,
  ];
  const [cameras] = useAtom(AvailableCamerasAtom);
  const [, setStream] = useAtom(ShareStream) as [
    any,
    (u: SetStateAction<MediaStream | null>) => void,
  ];
  const [, setStreamType] = useAtom(StreamTypeAtom) as [
    any,
    (u: SetStateAction<'webcam' | 'screenshare' | null>) => void,
  ];

  const handleSelectCamera = async (deviceId: string) => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {deviceId: {exact: deviceId}},
      });
      setStreamType('webcam');
      setStream(stream);
      setShow(false);
    } catch (err) {
      console.error('Fehler beim Auswählen der Kamera:', err);
      alert(
        'Die ausgewählte Kamera konnte nicht gestartet werden. Bitte versuchen Sie es erneut oder wählen Sie eine andere Kamera.',
      );
    }
  };

  if (!show) {
    return null;
  }

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center"
      aria-modal="true"
      role="dialog">
      <div className="bg-[var(--bg-color)] p-6 rounded-lg shadow-xl w-full max-w-sm">
        <h2 className="text-lg font-bold mb-4">Kamera auswählen</h2>
        <div className="flex flex-col gap-3">
          {cameras.map((camera, index) => (
            <button
              key={camera.deviceId}
              className="button text-left justify-start"
              onClick={() => handleSelectCamera(camera.deviceId)}>
              {camera.label || `Kamera ${index + 1}`}
            </button>
          ))}
        </div>
        <div className="mt-6 flex justify-end">
          <button
            className="secondary"
            onClick={() => setShow(false)}>
            Abbrechen
          </button>
        </div>
      </div>
    </div>
  );
}