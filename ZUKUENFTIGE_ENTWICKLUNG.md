# Zukünftige Entwicklungspläne

Dieses Dokument skizziert geplante Funktionen und architektonische Weiterentwicklungen für diese Anwendung.

## 1. Flexible Modell-Architektur (Online & Offline)

**Ziel:** Die feste Kopplung an die Gemini API wird aufgelöst, um die App in eine universelle Analyse-Plattform zu verwandeln.

**Konzeptioneller Plan:**
- **Adapter-Schicht:** Einführung einer `ModelAdapter`-Abstraktionsschicht. Jedes KI-Modell (online oder offline) erhält einen eigenen Adapter, der eine einheitliche Schnittstelle implementiert (z.B. `generate(prompt, image, options)`).
- **Beispiel-Adapter:**
    - `GeminiAdapter`: Beinhaltet die bestehende Logik.
    - `OllamaAdapter`: Ermöglicht die Anbindung an lokal laufende Modelle über einen `fetch`-Aufruf an den Ollama-Server (z.B. `http://localhost:11434/api/generate`).
- **UI-Integration:** Ein neues Dropdown-Menü in der Benutzeroberfläche erlaubt dem Benutzer die Auswahl des aktiven Modells (z.B. "Gemini Cloud", "Lokales Modell (Ollama)").
- *requesty zugang***

## 2. Spezialisierter Echtzeit-Agent mit YOLO-E

**Ziel:** Die App wird um eine extrem schnelle, lokale Echtzeit-Objekterkennung für Live-Video-Streams erweitert. Dies wird durch ein hybrides KI-System erreicht, das schnelle Reflexe mit tiefem Verständnis kombiniert.

- **Der "Reflex" (YOLO-E):** Ein hocheffizientes, spezialisiertes Modell, das offline läuft und auf die Erkennung von Objekten in Echtzeit trainiert ist.
- **Der "Verstand" (Gemini/LLM):** Das universelle Cloud-Modell, das komplexe, unstrukturierte Fragen beantworten und schlussfolgern kann.

**Konzeptioneller Plan:**
1.  **Lokale Implementierung:** Zunächst wird YOLO-E direkt auf dem Laptop des Benutzers ausgeführt und greift auf die eingebaute **Webcam** zu. Dies ermöglicht eine einfache Implementierung ohne zusätzliche Hardware.
2.  **Live-Analysemodus:** Ein neuer Button "YOLO-Erkennung (Live)" startet den Prozess.
3.  **Echtzeit-Verarbeitung:** Der Webcam-Stream wird lokal von YOLO-E verarbeitet. Erkannte Objekte werden sofort als Begrenzungsrahmen in der App angezeigt.
4.  **"Handoff" an den Verstand:** Der Benutzer kann auf einen von YOLO-E erkannten Rahmen zeigen und eine komplexe Folgefrage im Chat stellen (z.B. "Beschreibe das Objekt in Box #3 genauer").
5.  **Synthese:** Die App sendet einen hochauflösenden Ausschnitt des Objekts und die Frage an das Haupt-KI-Modell (z.B. Gemini), um eine detaillierte, kontextbezogene Antwort zu erhalten.

**Referenz & Weiterführende Informationen:**
- Die technische Grundlage für dieses Konzept wird in diesem hervorragenden Leitfaden beschrieben: [Custom Object Detection Models without Training | YOLOE and Raspberry Pi](https://core-electronics.com.au/guides/raspberry-pi/custom-object-detection-models-without-training-yoloe-and-raspberry-pi/)

## 3. Anbindung an einen Browser-Agenten für gezieltes Screen-Capturing

**Ziel:** Anstatt eine eigene, aufwendige Browser-Erweiterung zu entwickeln, wird die App an einen bestehenden oder zukünftigen, spezialisierten **mcpBrowserAgent** angebunden.

**Konzeptioneller Plan:**
- **Integration statt Eigenentwicklung:** Die App implementiert eine Schnittstelle, um Befehle und Bilddaten von einem externen Browser-Agenten zu empfangen.
- **Gezielte Analyse:** Dies ermöglicht es dem Benutzer, auf einer beliebigen Webseite ein spezifisches Element (z.B. ein Bild, einen Produkt-Container, einen Textabschnitt) auszuwählen und nur diesen Ausschnitt zur Analyse an unsere App zu senden.
- **Vorteil:** Enorme Reduzierung des Entwicklungsaufwands bei gleichzeitiger Steigerung der Funktionalität. Die App fokussiert sich auf ihre Kernkompetenz – die Bildanalyse – und überlässt das Browsing einem spezialisierten Werkzeug.
