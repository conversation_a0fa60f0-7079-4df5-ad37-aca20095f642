/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
// Copyright 2024 Google LLC

// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at

//     https://www.apache.org/licenses/LICENSE-2.0

// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import {GoogleGenAI, Chat, GenerateContentResponse} from '@google/genai';
import {
  AIProvider,
  AIChat,
  GenerateContentParams,
  GenerateContentResult,
  SendMessageParams,
  MessageResult,
  ChatConfig,
  ProviderError,
  ProviderConfig,
} from './types';

class GeminiChatSession implements AIChat {
  private chat: Chat;

  constructor(ai: GoogleGenAI, config: ChatConfig) {
    this.chat = ai.chats.create({
      model: config.model || 'gemini-2.5-flash',
      config: {
        systemInstruction: config.systemInstruction,
        temperature: config.temperature,
      },
    });
  }

  async sendMessage(params: SendMessageParams): Promise<MessageResult> {
    try {
      let apiMessage: any;

      if (typeof params.message === 'string') {
        apiMessage = params.message;
      } else {
        // Handle multimodal content
        apiMessage = params.message.map((item) => {
          if (item.type === 'text' && item.text) {
            return {text: item.text};
          } else if (item.type === 'image' && item.imageData) {
            return {
              inlineData: {
                data: item.imageData,
                mimeType: 'image/png', // Assuming PNG for now
              },
            };
          }
          return {}; // Should be filtered out
        });
      }

      const result: GenerateContentResponse = await this.chat.sendMessage({
        message: apiMessage,
      });
      return {text: result.text};
    } catch (error) {
      throw new ProviderError(
        `Gemini chat error: ${
          error instanceof Error ? error.message : String(error)
        }`,
        'gemini',
        'chat_error',
      );
    }
  }
}

export class GeminiProvider implements AIProvider {
  id = 'gemini';
  name: string;
  supportsVision = true;
  private ai: GoogleGenAI;
  private config: ProviderConfig;

  constructor(config: ProviderConfig) {
    if (!config.apiKey) {
      throw new Error(
        'Gemini API key is required. Please set API_KEY in your environment.',
      );
    }
    this.ai = new GoogleGenAI({apiKey: config.apiKey});
    this.config = config;
    this.name = config.name || 'Gemini';
  }

  createChat(config: ChatConfig): AIChat {
    return new GeminiChatSession(this.ai, {
      ...config,
      model: this.config.model,
    });
  }

  async generateContent(
    params: GenerateContentParams,
  ): Promise<GenerateContentResult> {
    try {
      const contents: any[] = [];
      if (params.prompt) {
        contents.push({text: params.prompt});
      }
      if (params.image) {
        contents.unshift({
          inlineData: {data: params.image, mimeType: 'image/png'},
        });
      }

      const result = await this.ai.models.generateContent({
        model: this.config.model || 'gemini-2.5-flash',
        contents: {parts: contents},
        config: {...params.config},
      });

      return {
        text: result.text,
        rawResponse: result,
      };
    } catch (error) {
      throw new ProviderError(
        `Gemini API error: ${
          error instanceof Error ? error.message : String(error)
        }`,
        'gemini',
        'api_error',
      );
    }
  }
}
