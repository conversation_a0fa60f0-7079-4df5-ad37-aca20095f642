/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
/* tslint:disable */
// Copyright 2024 Google LLC

// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at

//     https://www.apache.org/licenses/LICENSE-2.0

// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import {BoundingBox2DType, BoundingBoxMaskType} from './Types';

export function getSvgPathFromStroke(stroke: number[][]) {
  if (!stroke.length) return '';

  const d = stroke.reduce(
    (acc, [x0, y0], i, arr) => {
      const [x1, y1] = arr[(i + 1) % arr.length];
      acc.push(x0, y0, (x0 + x1) / 2, (y0 + y1) / 2);
      return acc;
    },
    ['M', ...stroke[0], 'Q'],
  );

  d.push('Z');
  return d.join(' ');
}

export function loadImage(src: string) {
  return new Promise<HTMLImageElement>((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous'; // Fixes tainted canvas error
    img.src = src;
    img.onload = () => resolve(img);
    img.onerror = reject;
  });
}

/**
 * Checks if a given text contains keywords that suggest a visual detail query.
 * @param text The text to analyze.
 * @returns True if visual keywords are found, false otherwise.
 */
export function hasVisualKeywords(text: string): boolean {
  const keywords = [
    'farbe',
    'textur',
    'aussehen',
    'details zu',
    'wie sieht',
    'beschreibe',
    'rot',
    'grün',
    'blau',
    'gelb',
    'was ist das für',
    'erkennst du',
  ];
  const lowerCaseText = text.toLowerCase();
  return keywords.some((keyword) => lowerCaseText.includes(keyword));
}