# Lokale Entwicklung Setup

## Voraussetzungen
- Node.js (Version 18 oder höher)
- npm oder yarn

## Setup für OpenRouter

### 1. Dependencies installieren
```bash
npm install
```

### 2. Environment Variablen konfigurieren
Bearbeiten Sie die `.env.local` Datei und setzen Sie Ihren OpenRouter API Key:

```
GEMINI_API_KEY=PLACEHOLDER_API_KEY
OPENROUTER_API_KEY=YOUR_ACTUAL_OPENROUTER_API_KEY_HERE
```

**Wichtig**: Ersetzen Sie `YOUR_ACTUAL_OPENROUTER_API_KEY_HERE` mit Ihrem echten OpenRouter API Key.

### 3. OpenRouter API Key erhalten
1. Gehen Sie zu [OpenRouter.ai](https://openrouter.ai/)
2. Erstellen Sie ein Konto oder loggen Sie sich ein
3. Navigieren Sie zu den API Keys
4. Erstellen Sie einen neuen API Key
5. <PERSON><PERSON><PERSON> Sie den Key in die `.env.local` Datei

### 4. Anwendung starten
```bash
npm run dev
```

Die Anwendung läuft dann auf `http://localhost:5173`

## Wichtige Änderungen für lokale Entwicklung

### CORS-Problem gelöst
- **Problem**: OpenRouter API verursacht CORS-Fehler bei direkten Aufrufen
- **Lösung**: Vite Proxy konfiguriert (`/api/openrouter` → `https://openrouter.ai/api/v1`)

### Standard-Provider geändert
- **Vorher**: Gemini (für AI Studio)
- **Jetzt**: OpenRouter (für lokale Entwicklung)

### Proxy-Konfiguration
Die `vite.config.ts` enthält jetzt einen Proxy für OpenRouter:
```typescript
server: {
  proxy: {
    '/api/openrouter': {
      target: 'https://openrouter.ai/api/v1',
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/api\/openrouter/, ''),
      headers: {
        'Origin': 'https://openrouter.ai'
      }
    }
  }
}
```

## Verfügbare Modelle
- **OpenRouter**: `qwen/qwen-vl-plus:free` (Standard)
- **Gemini**: `gemini-2.5-flash` (falls Gemini API Key gesetzt)

## Troubleshooting

### "OpenRouter API key is not configured"
- Überprüfen Sie, ob `OPENROUTER_API_KEY` in `.env.local` gesetzt ist
- Starten Sie den Dev-Server neu nach Änderungen an `.env.local`

### CORS-Fehler
- Stellen Sie sicher, dass Sie `npm run dev` verwenden (nicht `npm run build` + `npm run preview`)
- Der Proxy funktioniert nur im Development-Modus

### Modell nicht verfügbar
- Überprüfen Sie auf [OpenRouter.ai](https://openrouter.ai/models), ob das Modell verfügbar ist
- Ändern Sie das Modell in `providers/config.ts` falls nötig

## Scripts
- `npm run dev` - Startet Development Server mit Proxy
- `npm run build` - Erstellt Production Build
- `npm run preview` - Vorschau des Production Builds (ohne Proxy!)
