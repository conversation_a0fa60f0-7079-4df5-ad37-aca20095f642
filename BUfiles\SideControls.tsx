/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
/* tslint:disable */
// Copyright 2024 Google LLC

// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at

//     https://www.apache.org/licenses/LICENSE-2.0

// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import {useAtom} from 'jotai';
import type {SetStateAction} from 'jotai';
import {
  AvailableCamerasAtom,
  BumpSessionAtom,
  DrawModeAtom,
  Esp32EndpointAtom,
  ImageSentAtom,
  ImageSrcAtom,
  IsEsp32ImageAtom,
  IsFetchingEsp32ImageAtom,
  IsUploadedImageAtom,
  ShareStream,
  ShowCameraSelectionAtom,
  StreamTypeAtom,
} from './atoms';
import {useResetState} from './hooks';
import {ScreenshareButton} from './ScreenshareButton';

export function SideControls() {
  const [, setImageSrc] = useAtom(ImageSrcAtom) as [
    any,
    (u: SetStateAction<string | null>) => void,
  ];
  const [drawMode, setDrawMode] = useAtom(DrawModeAtom) as [
    boolean,
    (u: SetStateAction<boolean>) => void,
  ];
  const [, setIsUploadedImage] = useAtom(IsUploadedImageAtom) as [
    any,
    (u: SetStateAction<boolean>) => void,
  ];
  const [, setBumpSession] = useAtom(BumpSessionAtom) as [
    any,
    (u: SetStateAction<number>) => void,
  ];
  const [, setImageSent] = useAtom(ImageSentAtom) as [
    any,
    (u: SetStateAction<boolean>) => void,
  ];
  const resetState = useResetState();

  const [esp32Endpoint, setEsp32Endpoint] = useAtom(Esp32EndpointAtom) as [
    string,
    (u: SetStateAction<string>) => void,
  ];
  const [isFetchingEsp32Image, setIsFetchingEsp32Image] = useAtom(
    IsFetchingEsp32ImageAtom,
  ) as [boolean, (u: SetStateAction<boolean>) => void];
  const [, setIsEsp32Image] = useAtom(IsEsp32ImageAtom) as [
    any,
    (u: SetStateAction<boolean>) => void,
  ];
  const [, setStream] = useAtom(ShareStream) as [
    any,
    (u: SetStateAction<MediaStream | null>) => void,
  ];
  const [, setStreamType] = useAtom(StreamTypeAtom) as [
    any,
    (u: SetStateAction<'webcam' | 'screenshare' | null>) => void,
  ];
  const [, setShowCameraSelection] = useAtom(ShowCameraSelectionAtom) as [
    any,
    (u: SetStateAction<boolean>) => void,
  ];
  const [, setAvailableCameras] = useAtom(AvailableCamerasAtom) as [
    any,
    (u: SetStateAction<MediaDeviceInfo[]>) => void,
  ];

  const handleFetchFromEsp32 = async () => {
    if (!esp32Endpoint) {
      alert('Bitte geben Sie die ESP32-Kamera-Endpunkt-URL ein.');
      return;
    }
    setIsFetchingEsp32Image(true);
    // setImageSent(false); // Handled by resetState
    setStream(null); // Ensure screenshare is off

    try {
      const response = await fetch(esp32Endpoint);
      if (!response.ok) {
        throw new Error(
          `Fehler beim Abrufen des Bildes von ESP32: ${response.status} ${response.statusText}`,
        );
      }
      const imageBlob = await response.blob();
      if (!imageBlob.type.startsWith('image/')) {
        throw new Error(
          'Die abgerufenen Daten sind kein Bild. Bitte überprüfen Sie den ESP32-Endpunkt.',
        );
      }
      const reader = new FileReader();
      reader.onloadend = () => {
        resetState(); // Reset other states before setting new image
        setImageSrc(reader.result as string);
        setIsUploadedImage(false); // Explicitly set as not an "upload" in the traditional sense
        setIsEsp32Image(true);
        // setBumpSession((prev) => prev + 1); // Handled by resetState
        setIsFetchingEsp32Image(false);
      };
      reader.onerror = () => {
        throw new Error('Fehler beim Lesen der Bilddaten von ESP32.');
      };
      reader.readAsDataURL(imageBlob);
    } catch (error) {
      console.error('Fehler beim Abrufen von ESP32:', error);
      alert(
        `Fehler beim Abrufen von ESP32: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
      setIsFetchingEsp32Image(false);
    }
  };

  const handleWebcamStart = async () => {
    resetState();
    try {
      // Check for available devices first.
      const devices = await navigator.mediaDevices.enumerateDevices();
      const videoDevices = devices.filter((d) => d.kind === 'videoinput');

      if (videoDevices.length === 0) {
        alert('Keine Kamera gefunden.');
        return;
      }

      // If there's only one camera, use it directly.
      if (videoDevices.length === 1) {
        const stream = await navigator.mediaDevices.getUserMedia({
          video: true,
        });
        setStreamType('webcam');
        setStream(stream);
      } else {
        // If there are multiple cameras, show the selection modal.
        setAvailableCameras(videoDevices);
        setShowCameraSelection(true);
      }
    } catch (err) {
      console.error('Fehler beim Zugriff auf die Webcam:', err);
      if (
        err instanceof Error &&
        (err.name === 'NotAllowedError' || err.name === 'PermissionDeniedError')
      ) {
        alert(
          'Der Zugriff auf die Kamera wurde verweigert. Bitte erlauben Sie den Zugriff in den Browsereinstellungen und laden Sie die Seite neu.',
        );
      } else {
        alert(
          'Auf die Webcam konnte nicht zugegriffen werden. Bitte stellen Sie sicher, dass die Berechtigungen erteilt wurden und keine andere Anwendung die Kamera verwendet.',
        );
      }
    }
  };

  return (
    <div className="flex flex-col gap-3">
      <label className="flex items-center button bg-[#3B68FF] px-12 !text-white !border-none">
        <input
          className="hidden"
          type="file"
          accept=".jpg, .jpeg, .png, .webp"
          aria-label="Ein Bild hochladen"
          onChange={(e) => {
            const file = e.target.files?.[0];
            if (file) {
              const reader = new FileReader();
              reader.onload = (event) => {
                resetState();
                setImageSrc(event.target?.result as string);
                setIsUploadedImage(true);
                setIsEsp32Image(false);
                // setImageSent(false); // Handled by resetState
                // setBumpSession((prev) => prev + 1); // Handled by resetState
              };
              reader.readAsDataURL(file);
            }
          }}
        />
        <div>Bild hochladen</div>
      </label>

      <div className="flex flex-col gap-2 mt-4 pt-4 border-t">
        <label htmlFor="esp32-url" className="text-sm font-medium">
          ESP32-Kamera-URL:
        </label>
        <input
          id="esp32-url"
          type="text"
          value={esp32Endpoint}
          onChange={(e) => setEsp32Endpoint(e.target.value)}
          placeholder="http://<ESP32_IP>/capture"
          className="w-full p-2 border rounded-md bg-[var(--input-color)]"
          aria-describedby="esp32-url-description"
        />
        <p
          id="esp32-url-description"
          className="text-xs text-[var(--text-color-secondary)]">
          Endpunkt, der ein einzelnes JPG/PNG-Bild bereitstellt.
        </p>
        <button
          onClick={handleFetchFromEsp32}
          disabled={isFetchingEsp32Image}
          className="button flex gap-3 justify-center items-center"
          aria-live="polite">
          {isFetchingEsp32Image ? (
            <>
              <svg
                className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24">
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Abrufen...
            </>
          ) : (
            <>
              <div className="text-lg">📷</div>
              <div>Von ESP32 CAM abrufen</div>
            </>
          )}
        </button>
      </div>

      <div className="flex flex-col gap-2 mt-4 pt-4 border-t">
        <h3 className="text-sm font-medium text-[var(--text-color-secondary)]">
          Live-Quellen
        </h3>
        <button
          className="button flex gap-3 justify-center items-center"
          onClick={handleWebcamStart}>
          <div className="text-lg">📷</div>
          <div>Webcam starten</div>
        </button>
        <ScreenshareButton />
      </div>

      <div className="hidden">
        {' '}
        {/* Keep existing hidden controls if they were intentional */}
        <button
          className="button flex gap-3 justify-center items-center"
          onClick={() => {
            setDrawMode(!drawMode);
          }}>
          <div className="text-lg"> 🎨</div>
          <div>Auf Bild zeichnen</div>
        </button>
      </div>
    </div>
  );
}