import path from 'path';
import { defineConfig, loadEnv } from 'vite';

export default defineConfig(({ mode }) => {
    const env = loadEnv(mode, '.', '');
    return {
      define: {
        'process.env.API_KEY': JSON.stringify(env.GEMINI_API_KEY),
        'process.env.GEMINI_API_KEY': JSON.stringify(env.GEMINI_API_KEY),
        'process.env.OPENROUTER_API_KEY': JSON.stringify(env.OPENROUTER_API_KEY)
      },
      resolve: {
        alias: {
          '@': path.resolve(__dirname, '.'),
        }
      },
      server: {
        proxy: {
          '/api/openrouter': {
            target: 'https://openrouter.ai/api/v1',
            changeOrigin: true,
            rewrite: (path) => path.replace(/^\/api\/openrouter/, ''),
            headers: {
              'Origin': 'https://openrouter.ai'
            }
          }
        }
      }
    };
});
