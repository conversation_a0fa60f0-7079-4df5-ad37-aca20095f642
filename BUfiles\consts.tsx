/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
/* tslint:disable */
// Copyright 2024 Google LLC

// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at

//     https://www.apache.org/licenses/LICENSE-2.0

// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

export const colors = [
  'rgb(0, 0, 0)',
  'rgb(255, 255, 255)',
  'rgb(213, 40, 40)',
  'rgb(250, 123, 23)',
  'rgb(240, 186, 17)',
  'rgb(8, 161, 72)',
  'rgb(26, 115, 232)',
  'rgb(161, 66, 244)',
];

function hexToRgb(hex: string) {
  const r = parseInt(hex.substring(1, 3), 16);
  const g = parseInt(hex.substring(3, 5), 16);
  const b = parseInt(hex.substring(5, 7), 16);
  return [r, g, b];
}

export const pointColors = [
  '#E6194B', // Red
  '#3CB44B', // Green
  '#FFE119', // Yellow
  '#4363D8', // Blue
  '#F58231', // Orange
  '#911EB4', // Purple
  '#42D4F4', // Cyan
  '#F032E6', // Magenta
  '#BFEF45', // Lime
  '#FABED4', // Pink
  '#469990', // Teal
  '#DCBEFF', // Lavender
  '#9A6324', // Brown
  '#800000', // Maroon
  '#808000', // Olive
  '#000075', // Navy
];

export const segmentationColors = [
  '#E6194B',
  '#3C89D0',
  '#3CB44B',
  '#FFE119',
  '#911EB4',
  '#42D4F4',
  '#F58231',
  '#F032E6',
  '#BFEF45',
  '#469990',
];
export const segmentationColorsRgb = segmentationColors.map((c) => hexToRgb(c));

export const imageOptionsPromise: Promise<string[]> = Promise.all(
  [
    'origami.jpg',
    'pumpkins.jpg',
    'clock.jpg',
    'socks.jpg',
    'breakfast.jpg',
  ].map(async (i) =>
    URL.createObjectURL(
      await (
        await fetch(
          `https://www.gstatic.com/aistudio/starter-apps/bounding-box/${i}`,
        )
      ).blob(),
    ),
  ),
);

export const lineOptions = {
  size: 8,
  thinning: 0,
  smoothing: 0,
  streamline: 0,
  simulatePressure: false,
};

const detailedLabelInstruction = `Für die Beschriftung ("label") verwende eine spezifische, detaillierte Textbezeichnung, die das Objekt eindeutig identifiziert.möglichst in deutscher Sprache. Schließe relevante Attribute wie Farbe oder andere besondere Merkmale mit ein (z.B. "rotes Auto", "gestreiftes Hemd").`;

export const defaultPromptParts = {
  '2D-Begrenzungsrahmen': [
    'Erkenne mit 2D-Begrenzungsrahmen folgende Objekte: ',
    'Gegenständen',
    `. Gib nicht mehr als 20 Objekte zurück.Gib das Ergebnis als JSON-Liste aus. Jeder Eintrag in der Liste muss einen Schlüssel "box_2d" für den Begrenzungsrahmen und einen Schlüssel "label" enthalten. ${detailedLabelInstruction}`,
  ],
  Segmentierungsmasken: [
    `Erstelle Segmentierungsmasken für folgende Objekte: `,
    'Gegenständen',
    `. Beschränke die Ausgabe auf maximal 20 Objekte. Gib eine JSON-Liste von Segmentierungsmasken aus, bei der jeder Eintrag den 2D-Begrenzungsrahmen im Schlüssel "box_2d", die Segmentierungsmaske im Schlüssel "mask" und die Textbezeichnung im Schlüssel "label" enthält. ${detailedLabelInstruction}`,
  ],
  Punkte: [
    'Zeige mit Punkten auf folgende Objekte: ',
    'Gegenständen',
    `. Gib nicht mehr als 10 Punkte zurück. Die Antwort muss dem JSON-Format folgen: [{"point": <punkt>, "label": <label1>}, ...]. ${detailedLabelInstruction} Die Punkte sind im [y, x]-Format und auf 0-1000 normalisiert.`,
  ],
};

export const defaultPrompts = {
  '2D-Begrenzungsrahmen': defaultPromptParts['2D-Begrenzungsrahmen'].join(''),
  Segmentierungsmasken: defaultPromptParts.Segmentierungsmasken.join(''),
  Punkte: defaultPromptParts.Punkte.join(''),
};

const safetyLevel = 'only_high';

export const safetySettings = new Map();

safetySettings.set('harassment', safetyLevel);
safetySettings.set('hate_speech', safetyLevel);
safetySettings.set('sexually_explicit', safetyLevel);
safetySettings.set('dangerous_content', safetyLevel);
safetySettings.set('civic_integrity', safetyLevel);