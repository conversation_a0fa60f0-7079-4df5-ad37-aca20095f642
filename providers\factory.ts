/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
// Copyright 2024 Google LLC

// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at

//     https://www.apache.org/licenses/LICENSE-2.0

// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import {AIProvider, ProviderConfig} from './types';
import {GeminiProvider} from './gemini-provider';
import {OpenRouterProvider} from './openrouter-provider';

export class ProviderFactory {
  static createProvider(config: ProviderConfig): AIProvider {
    switch (config.type) {
      case 'gemini':
        return new GeminiProvider(config);
      case 'openrouter':
        return new OpenRouterProvider(config);
      default:
        // Ensure exhaustive check, though config validation should prevent this.
        const exhaustiveCheck: never = config.type;
        throw new Error(`Unknown provider type: ${exhaustiveCheck}`);
    }
  }
}
