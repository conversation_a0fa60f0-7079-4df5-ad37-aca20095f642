/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
// Copyright 2024 Google LLC

// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at

//     https://www.apache.org/licenses/LICENSE-2.0

// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import {
  AIProvider,
  AIChat,
  GenerateContentParams,
  GenerateContentResult,
  SendMessageParams,
  MessageResult,
  ChatConfig,
  ProviderError,
  ProviderConfig,
  MultimodalContent,
} from './types';

interface OpenRouterMessage {
  role: 'system' | 'user' | 'assistant';
  content:
    | string
    | ({type: 'text'; text: string} | {type: 'image_url'; image_url: {url: string}})[];
}

class OpenRouterChat implements AIChat {
  private history: OpenRouterMessage[] = [];
  private provider: OpenRouterProvider;
  private config: ChatConfig;

  constructor(provider: OpenRouterProvider, config: ChatConfig) {
    this.provider = provider;
    this.config = config;

    if (config.systemInstruction) {
      this.history.push({role: 'system', content: config.systemInstruction});
    }
  }

  async sendMessage(params: SendMessageParams): Promise<MessageResult> {
    const userMessage = this.provider.buildOpenRouterMessageContent(
      params.message,
    );
    this.history.push({role: 'user', content: userMessage});
    const result = await this.provider.makeRequest(
      this.history,
      this.config.model,
      this.config.temperature,
    );
    this.history.push({role: 'assistant', content: result.text});
    return result;
  }
}

export class OpenRouterProvider implements AIProvider {
  id = 'openrouter';
  name: string;
  supportsVision = true;
  private config: ProviderConfig;

  constructor(config: ProviderConfig) {
    if (!config.apiKey) {
      console.warn(
        'OpenRouter API key is not set. Please set OPENROUTER_API_KEY in your environment for it to work.',
      );
    }
    if (!config.baseUrl) {
      throw new Error(
        'OpenRouter provider requires a baseUrl in its configuration.',
      );
    }
    this.config = config;
    this.name = config.name || 'OpenRouter';
  }

  createChat(config: ChatConfig): AIChat {
    return new OpenRouterChat(this, {...config, model: this.config.model});
  }

  buildOpenRouterMessageContent(
    message: string | MultimodalContent[],
  ):
    | string
    | ({type: 'text'; text: string} | {type: 'image_url'; image_url: {url: string}})[] {
    if (typeof message === 'string') {
      return message;
    }

    const content: ({type: 'text'; text: string} | {type: 'image_url'; image_url: {url: string}})[] =
      [];
    for (const item of message) {
      if (item.type === 'text' && item.text) {
        content.push({type: 'text', text: item.text});
      } else if (item.type === 'image') {
        const imageUrl = item.imageUrl
          ? item.imageUrl
          : `data:image/png;base64,${item.imageData}`;
        content.push({type: 'image_url', image_url: {url: imageUrl}});
      }
    }
    return content;
  }

  async makeRequest(
    messages: OpenRouterMessage[],
    model?: string,
    temperature?: number,
  ): Promise<GenerateContentResult> {
    if (!this.config.apiKey) {
      throw new ProviderError(
        'OpenRouter API key is not configured.',
        this.id,
        'config_error',
      );
    }

    const requestBody = {
      model: model || this.config.model,
      messages,
      temperature: temperature ?? 0.7,
      max_tokens: 4000,
    };

    try {
      const response = await fetch(
        `${this.config.baseUrl}/chat/completions`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${this.config.apiKey}`,
            'HTTP-Referer':
              typeof window !== 'undefined'
                ? window.location.host
                : 'https://aistudio.google.com',
            'X-Title': 'KI Kamera Assistent',
          },
          body: JSON.stringify(requestBody),
        },
      );

      if (!response.ok) {
        const errorText = await response.text();
        throw new ProviderError(
          `API error: ${errorText}`,
          this.id,
          'api_error',
          response.status,
        );
      }

      const data = await response.json();
      return {text: data.choices[0].message.content};
    } catch (error) {
      if (error instanceof ProviderError) {
        throw error;
      }
      throw new ProviderError(
        `${error instanceof Error ? error.message : String(error)}`,
        this.id,
        'network_error',
      );
    }
  }

  async generateContent(
    params: GenerateContentParams,
  ): Promise<GenerateContentResult> {
    const messages: OpenRouterMessage[] = [];
    if (params.config?.systemInstruction) {
      messages.push({
        role: 'system',
        content: params.config.systemInstruction,
      });
    }

    const content: ({type: 'text'; text: string} | {type: 'image_url'; image_url: {url: string}})[] =
      [];
    if (params.prompt) {
      content.push({type: 'text', text: params.prompt});
    }
    if (params.image) {
      content.push({
        type: 'image_url',
        image_url: {url: `data:image/png;base64,${params.image}`},
      });
    }
    messages.push({role: 'user', content});

    return this.makeRequest(
      messages,
      this.config.model,
      params.config?.temperature,
    );
  }
}
