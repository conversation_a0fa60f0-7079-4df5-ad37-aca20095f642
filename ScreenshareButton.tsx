/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
*/
/* tslint:disable */
// Copyright 2024 Google LLC

// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at

//     https://www.apache.org/licenses/LICENSE-2.0

// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import {useAtom} from 'jotai';
import type {SetStateAction} from 'jotai';
import {ShareStream, StreamTypeAtom} from './atoms';
import {useResetState} from './hooks';

export function ScreenshareButton() {
  const [, setStream] = useAtom(ShareStream) as [
    any,
    (u: SetStateAction<MediaStream | null>) => void,
  ];
  const [, setStreamType] = useAtom(StreamTypeAtom) as [
    any,
    (u: SetStateAction<'webcam' | 'screenshare' | null>) => void,
  ];
  const resetState = useResetState();

  return (
    <button
      className="button flex gap-3 justify-center items-center"
      onClick={() => {
        resetState();
        navigator.mediaDevices
          .getDisplayMedia({video: true})
          .then((stream) => {
            setStreamType('screenshare');
            setStream(stream);
          })
          .catch((err) => {
            console.error('Fehler bei der Bildschirmfreigabe:', err);
            if (
              err instanceof Error &&
              (err.name === 'NotAllowedError' ||
                err.name === 'PermissionDeniedError')
            ) {
              alert(
                'Die Berechtigung zur Bildschirmfreigabe wurde verweigert. Bitte erteilen Sie die Berechtigung, um diese Funktion zu nutzen.',
              );
            } else {
              alert(
                'Die Bildschirmfreigabe konnte nicht gestartet werden. Bitte versuchen Sie es erneut.',
              );
            }
          });
      }}>
      <div className="text-lg">🖥️</div>
      <div>Screenshare</div>
    </button>
  );
}